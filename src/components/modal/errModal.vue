<script lang="ts">
import { Vue, Component, Prop, Ref } from 'vue-property-decorator'
import Modal from '@/components/modal/Modal.vue'
import { State } from 'vuex-class'

@Component({
  components: { Modal },
})
export default class errModal extends Vue {

  isVerifyTime = false
  isTimeLabel = false
  describe = ''
  @Ref('modalRef') modalRef: any
  @State('configData') configData: any

  open(obj) {
    this.describe = obj.describe
    this.isVerifyTime = obj.isVerifyTime
    this.isTimeLabel = obj?.isTimeLabel ?? false
    this.modalRef.show = true
  }
}
</script>

<template>
  <modal
    :bg-obj="{ background: configData.imgList.answerCommonBg }"
    ref="modalRef"
    class="modal-dialog"
    :show-close-button="false"
  >
    <div class="title font_40 common_text_style_medium color_222222 text_align_center">温馨提示</div>
    <div class="err_info">
      <div
        class="err_text flex_column font_32 common_text_common_style_44 text_align_center"
        v-html="describe"
      ></div>
      <div v-if="!isTimeLabel"  class="btn-index flex_column font_32 common_text_common_style_44 "
        @click="modalRef.show = false"
      >我知道了</div>
      <div class="close" @click="modalRef.show = false"  v-if="!isTimeLabel"></div>
    </div>
  </modal>
</template>

<style scoped lang="less">
.modal-dialog {
  .title {
    position: absolute;
    top: 0.74rem;
    width: 100%;
  }
  .err_text {
    position: absolute;
    top: 1.86rem;
    width: 100%;
    color: #222222;
  }
  .btn-index {
    position: absolute;
    top: 3.16rem;
    left: 0.94rem;
    width: 3.66rem;
    height: 0.8rem;
    color: #FFFFFF;
    background: #FF9405;
    border-radius: 0.4rem;
  }
  .close{
    position: absolute;
    top: 0.19rem;
    right: 0.19rem;
    width: 0.21rem;
    height: 0.21rem;
    background: url('https://psm.bangdao-tech.com/interaction-putting/20316/img/20250428101741723099058163703915_w21_h21.png') no-repeat;
    background-size: 100% 100%;
  }
}
</style>
