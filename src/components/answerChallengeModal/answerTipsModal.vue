<script lang="ts">
import { Vue, Component, Prop, Ref } from 'vue-property-decorator'
import Modal from '@/components/modal/Modal.vue'
import { State } from 'vuex-class'
import { jumpTime } from '@/utils/debounce'

@Component({
  components: { Modal },
})
export default class errModal extends Vue {
  @Prop() pageTab:any
  @Ref('modalTipsRef') modalRef: any

  obj :any = {}

  open(obj) {
    this.obj = obj
    this.modalRef.show = true
  }

  @jumpTime()
  jumpOperation(){
    setTimeout(()=>{
      this.$emit('jumpOperation',this.obj.type)
    })

  }
}
</script>

<template>
  <modal
    :bg-obj="{ background: obj.title ?  pageTab.imgList.dialogBg : pageTab.imgList.dialogOtherBg }"
    ref="modalTipsRef"
    class="modal-dialog"
    :show-close-button="false"
  >
    <div class="title font_40 common_text_style_medium color_222222 text_align_center" v-if="obj.title">{{obj.title}}</div>
    <div class="err_info">
      <div class="err_text flex_column font_30 common_text_common_style text_align_center" :class="[obj.title ? '' : 'no_title']"
        v-html="obj.content"
      ></div>
      <div
        class="btn-index flex_column font_32 common_text_common_style "
        :class="[obj.title ? '' : 'no_btn_index']"
        @click="modalRef.show = false;jumpOperation()"
      >{{obj.btnText}}</div>
      <div class="close" @click="modalRef.show = false"></div>
    </div>
  </modal>
</template>

<style scoped lang="less">
.modal-dialog {
  .title {
    position: absolute;
    top: 0.51rem;
    width: 100%;
  }
  .err_text {
    position: absolute;
    top: 1.34rem;
    width: 100%;
    line-height: 0.42rem;
    color: #12253a;
  }

  .no_title{
    top: 0.61rem;
  }

  .btn-index {
    position: absolute;
    top: 3.16rem;
    margin: 0 0.5rem;
    width: 4.6rem;
    height: 0.8rem;
    color: #FFFFFF;
    background: #FF9405;
    border-radius: 0.4rem;
  }
  .no_btn_index{
    top: 2.22rem
  }
  .close{
    position: absolute;
    top: 0.19rem;
    right: 0.19rem;
    width: 0.21rem;
    height: 0.21rem;
  }
}
</style>
