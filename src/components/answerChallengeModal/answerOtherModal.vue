<script lang="ts">
import { Vue, Component, Prop, Ref } from 'vue-property-decorator'
import Modal from '@/components/modal/Modal.vue'
import { State } from 'vuex-class'
import { jumpTime } from '@/utils/debounce'

@Component({
  components: { Modal },
})
export default class errModal extends Vue {
  @Prop() pageTab:any
  @Ref('modalOtherRef') modalOtherRef: any

  obj :any = {}

  open(obj) {
    this.obj = obj
    this.modalOtherRef.show = true
  }

  @jumpTime()
  backOperation(){
    this.$emit('backOperation')
  }

  @jumpTime()
  jumpOperation(){
    this.$emit('jumpOperation')
  }
}
</script>

<template>
  <modal
    :bg-obj="{ background: obj.title ?  pageTab.imgList.dialogBg : pageTab.imgList.dialogOtherBg }"
    ref="modalOtherRef"
    class="modal-dialog"
    :show-close-button="false"
  >
    <div class="title font_40 common_text_style_medium color_222222 text_align_center" v-if="obj.title">{{obj.title}}</div>
    <div class="err_info">
      <div class="err_text flex_column font_30 common_text_common_style text_align_center" :class="[obj.title ? '' : 'no_title']" v-html="obj.content"></div>
      <div class="btn-index flex_between_center font_32 common_text_common_style " :class="[obj.title ? '' : 'no_btn_index']">
        <div class="left-btn flex_column" @click="modalOtherRef.show = false;backOperation()">{{obj.leftText}}</div>
        <div class="right-btn flex_column" @click="modalOtherRef.show = false;jumpOperation()">{{obj.rightText}}</div>
      </div>

      <div class="close" @click="modalOtherRef.show = false;backOperation()"></div>
    </div>
  </modal>
</template>

<style scoped lang="less">
.modal-dialog {
  .title {
    position: absolute;
    top: 0.51rem;
    width: 100%;
  }
  .err_text {
    position: absolute;
    top: 1.34rem;
    width: 100%;
    color: #12253a;
  }

  .no_title{
    top: 1.21rem;
  }

  .btn-index {
    position: absolute;
    top: 3.16rem;
    left: 0.5rem;
    width: 4.6rem;
    height: 0.8rem;
    .left-btn , .right-btn{
      width: 2.1rem;
      height: 0.8rem;
    }
    .left-btn{
      background-color: #ffffff;
      border-radius: 0.4rem;
      border: solid 1px #fd6e12;
      color: #fd6e12;
    }
    .right-btn{
      background-image: linear-gradient(90deg, #fece5a 0%, #fd6a0e 100%), linear-gradient(#fd6a0e, #fd6a0e);
      background-blend-mode: normal, normal;
      border-radius: 0.4rem;
      border: solid 1px #fd6e12;
      color: #ffffff;
    }

  }
  .no_btn_index{
    top: 2.3rem
  }
  .close{
    position: absolute;
    top: 0.19rem;
    right: 0.19rem;
    width: 0.21rem;
    height: 0.21rem;
  }
}
</style>
