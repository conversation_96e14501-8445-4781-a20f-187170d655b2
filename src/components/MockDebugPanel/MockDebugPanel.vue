<template>
  <div v-if="showPanel" class="mock-debug-panel">
    <div class="panel-header">
      <h3>接口模拟调试面板</h3>
      <button @click="togglePanel" class="close-btn">×</button>
    </div>
    
    <div class="panel-content">
      <!-- 基本控制 -->
      <div class="section">
        <h4>基本控制</h4>
        <div class="control-group">
          <label>
            <input 
              type="checkbox" 
              v-model="config.enabled" 
              @change="updateEnabled"
            />
            启用接口模拟
          </label>
        </div>
        
        <div class="control-group">
          <label>
            响应延迟 (ms):
            <input 
              type="number" 
              v-model.number="config.delay" 
              @change="updateDelay"
              min="0"
              max="5000"
            />
          </label>
        </div>
        
        <div class="control-group">
          <label>
            <input 
              type="checkbox" 
              v-model="config.logRequests" 
              @change="updateLogRequests"
            />
            记录请求日志
          </label>
        </div>
        
        <div class="control-group">
          <label>
            <input 
              type="checkbox" 
              v-model="config.mockAll" 
              @change="updateMockAll"
            />
            模拟所有接口
          </label>
        </div>
      </div>
      
      <!-- 接口列表 -->
      <div class="section">
        <h4>接口模拟配置</h4>
        <div class="api-list">
          <div 
            v-for="api in availableApis" 
            :key="api"
            class="api-item"
          >
            <label>
              <input 
                type="checkbox" 
                :checked="isApiEnabled(api)"
                @change="toggleApi(api, $event)"
                :disabled="config.mockAll"
              />
              <span class="api-name">{{ api }}</span>
            </label>
            <button 
              @click="editMockData(api)"
              class="edit-btn"
              :disabled="!hasMockData(api)"
            >
              编辑数据
            </button>
          </div>
        </div>
      </div>
      
      <!-- 状态信息 -->
      <div class="section">
        <h4>状态信息</h4>
        <div class="status-info">
          <p><strong>Service Worker:</strong> {{ status.swStatus }}</p>
          <p><strong>已启用接口:</strong> {{ enabledApisCount }}</p>
          <p><strong>模拟数据:</strong> {{ mockDataCount }} 个</p>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="section">
        <h4>操作</h4>
        <div class="action-buttons">
          <button @click="refreshStatus" class="btn">刷新状态</button>
          <button @click="clearCache" class="btn">清除缓存</button>
          <button @click="resetConfig" class="btn btn-danger">重置配置</button>
        </div>
      </div>
    </div>
    
    <!-- 数据编辑弹窗 -->
    <div v-if="showDataEditor" class="data-editor-overlay" @click="closeDataEditor">
      <div class="data-editor" @click.stop>
        <div class="editor-header">
          <h4>编辑模拟数据: {{ editingApi }}</h4>
          <button @click="closeDataEditor" class="close-btn">×</button>
        </div>
        <div class="editor-content">
          <textarea 
            v-model="editingData"
            class="data-textarea"
            placeholder="请输入 JSON 格式的模拟数据"
          ></textarea>
        </div>
        <div class="editor-actions">
          <button @click="saveDataChanges" class="btn btn-primary">保存</button>
          <button @click="closeDataEditor" class="btn">取消</button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 浮动按钮 -->
  <div v-if="!showPanel" class="float-button" @click="togglePanel">
    Mock
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { mockManager, MockConfig } from '@/utils/mockManager'

@Component({})
export default class MockDebugPanel extends Vue {
  showPanel = false
  showDataEditor = false
  editingApi = ''
  editingData = ''
  
  config: MockConfig = {
    enabled: false,
    delay: 500,
    logRequests: true,
    mockAll: false,
    enabledApis: []
  }
  
  status = {
    swStatus: 'Unknown'
  }
  
  availableApis = [
    'bangdao.gfActivity.home.page',
    'bangdao.gfActivity.verify.activity.time',
    'bangdao.gfActivity.ds.sms.send',
    'bangdao.gfActivity.common.login',
    'bangdao.gfActivity.qa.extract',
    'bangdao.gfActivity.qa.answer',
    'bangdao.gfActivity.submit',
    'bangdao.gfActivity.prize.query',
    'bangdao.gfActivity.lbs.judge',
    'bangdao.lifecycle.wxjsapi.signature'
  ]
  
  async mounted() {
    // 只在开发环境显示
    if (process.env.NODE_ENV !== 'development') {
      return
    }
    
    await this.loadConfig()
    await this.refreshStatus()
  }
  
  async loadConfig() {
    this.config = mockManager.getConfig()
  }
  
  async refreshStatus() {
    try {
      const status = await mockManager.getStatus()
      this.status.swStatus = status.enabled ? 'Active' : 'Inactive'
    } catch (error) {
      this.status.swStatus = 'Error'
      console.error('Failed to get status:', error)
    }
  }
  
  togglePanel() {
    this.showPanel = !this.showPanel
  }
  
  async updateEnabled() {
    if (this.config.enabled) {
      await mockManager.enable()
    } else {
      await mockManager.disable()
    }
    await this.refreshStatus()
  }
  
  async updateDelay() {
    await mockManager.setDelay(this.config.delay)
  }
  
  async updateLogRequests() {
    await mockManager.setLogRequests(this.config.logRequests)
  }
  
  async updateMockAll() {
    await mockManager.setMockAll(this.config.mockAll)
  }
  
  isApiEnabled(api: string): boolean {
    return this.config.enabledApis.includes(api)
  }
  
  async toggleApi(api: string, event: Event) {
    const target = event.target as HTMLInputElement
    if (target.checked) {
      await mockManager.addEnabledApi(api)
    } else {
      await mockManager.removeEnabledApi(api)
    }
    await this.loadConfig()
  }
  
  hasMockData(api: string): boolean {
    return !!mockManager.getMockData(api)
  }
  
  editMockData(api: string) {
    this.editingApi = api
    const data = mockManager.getMockData(api)
    this.editingData = JSON.stringify(data, null, 2)
    this.showDataEditor = true
  }
  
  closeDataEditor() {
    this.showDataEditor = false
    this.editingApi = ''
    this.editingData = ''
  }
  
  async saveDataChanges() {
    try {
      const data = JSON.parse(this.editingData)
      await mockManager.setMockData(this.editingApi, data)
      this.closeDataEditor()
      this.$toast('模拟数据保存成功')
    } catch (error) {
      this.$toast('JSON 格式错误，请检查数据格式')
    }
  }
  
  async clearCache() {
    await mockManager.clearCache()
    this.$toast('缓存已清除')
  }
  
  async resetConfig() {
    if (confirm('确定要重置所有配置吗？')) {
      await mockManager.reset()
      await this.loadConfig()
      await this.refreshStatus()
      this.$toast('配置已重置')
    }
  }
  
  get enabledApisCount(): number {
    return this.config.enabledApis.length
  }
  
  get mockDataCount(): number {
    return Object.keys(mockManager.getMockData()).length
  }
}
</script>

<style scoped lang="less">
.mock-debug-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  font-size: 14px;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
  
  h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
  }
  
  .close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    
    &:hover {
      color: #333;
    }
  }
}

.panel-content {
  max-height: calc(80vh - 60px);
  overflow-y: auto;
  padding: 16px;
}

.section {
  margin-bottom: 20px;
  
  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 4px;
  }
}

.control-group {
  margin-bottom: 12px;
  
  label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    
    input[type="checkbox"] {
      margin: 0;
    }
    
    input[type="number"] {
      width: 80px;
      padding: 4px 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  }
}

.api-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
}

.api-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
  
  label {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    
    .api-name {
      font-family: monospace;
      font-size: 12px;
    }
  }
  
  .edit-btn {
    padding: 4px 8px;
    font-size: 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover:not(:disabled) {
      background: #0056b3;
    }
    
    &:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  }
}

.status-info {
  p {
    margin: 4px 0;
    font-size: 12px;
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.btn {
  padding: 6px 12px;
  font-size: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f8f9fa;
  cursor: pointer;
  
  &:hover {
    background: #e9ecef;
  }
  
  &.btn-primary {
    background: #007bff;
    color: white;
    border-color: #007bff;
    
    &:hover {
      background: #0056b3;
    }
  }
  
  &.btn-danger {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
    
    &:hover {
      background: #c82333;
    }
  }
}

.float-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: #007bff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 9998;
  font-size: 12px;
  font-weight: bold;
  
  &:hover {
    background: #0056b3;
  }
}

.data-editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.data-editor {
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
  
  h4 {
    margin: 0;
    font-size: 16px;
  }
}

.editor-content {
  flex: 1;
  padding: 16px;
}

.data-textarea {
  width: 100%;
  height: 300px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  resize: vertical;
}

.editor-actions {
  padding: 12px 16px;
  border-top: 1px solid #ddd;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}
</style>
