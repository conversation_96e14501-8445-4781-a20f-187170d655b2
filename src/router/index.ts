import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const router = new VueRouter({
  routes: [
    {
      path: '/',
      name: 'index',
      meta: {
        title: '首页',
      },
      component: () => import('@/pages/index/index.vue'),
    },
    {
      path: '/answer',
      name: 'answer',
      meta: {
        title: '答题页 ',
      },
      component: () => import('@/pages/answer/index.vue'),
    },
    {
      path: '/taskVideo',
      name: 'taskVideo',
      meta: {
        title: '观看视频页  ',
      },
      component: () => import('@/pages/videoTask/index.vue'),
    },
    {
      path: '/login',
      name: 'login',
      meta: {
        title: '登录页',
      },
      component: () => import('@/pages/login/index.vue'),
    },
    {
      path: '/myAward',
      name: 'myAward',
      meta: {
        title: '我的奖品页',
      },
      component: () => import('@/pages/myAward/index.vue'),
    },
    {
      path: '/rule',
      name: 'rule',
      meta: {
        title: '规则页',
        noSchema: true,
        canAddModule: true,
      },
      component: () => import('@/pages/rule/index.vue'),
    },
  ],
})

export const loadView = (view, pageFilePath) => {
  // 路由懒加载
  if (pageFilePath && pageFilePath.endsWith('.vue')) {
    // 路径分割符必须拼接才有效否则不识别
    const pagePath = pageFilePath.replace(/^src\//, '').split('/')
    return resolve => require([`@/${pagePath.join('/')}`], resolve)
  } else {
    return resolve => require([`@/pages/${view}/${view}.vue`], resolve)
  }
}

export default router
