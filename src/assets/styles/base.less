*,
:after,
:before {
  -webkit-tap-highlight-color: transparent;
}
blockquote,
body,
dd,
div,
dl,
dt,
fieldset,
form,
h1,
h2,
h3,
h4,
h5,
h6,
input,
legend,
li,
ol,
p,
td,
textarea,
th,
ul,
button {
  margin: 0;
  padding: 0;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
fieldset,
img {
  border: 0;
}
li {
  list-style: none;
}
q:after,
q:before {
  content: '';
}
input:password {
  ime-mode: disabled;
}
:focus {
  outline: 0;
}
a,
img {
  -webkit-touch-callout: none;
}

body {
  margin: 0;
  font-family: 'Helvetica Neue', Helvetica, STHeiTi, sans-serif;
  -webkit-user-select: none;
  user-select: none;
  background-color: #f5f5f9;
}
body,
html {
  min-height: 100%;
}

body,
button,
input,
select,
textarea {
  font-size: 16px;
  line-height: 1.2;
  color: #333;
}
input,
button,
select,
textarea {
  padding: 0;
  background: transparent;
  -webkit-appearance: none;
  outline: none;
  border: none;
  background-color: transparent;
}
input {
  line-height: normal;
}
a {
  color: #108ee9;
  text-decoration: none;
}

.fn-clear:after {
  display: block;
  font-size: 0;
  content: ' ';
  clear: both;
  height: 0;
}
.fn-hide {
  display: none;
}
.fn-show {
  display: block;
}
.vs-hide {
  visibility: hidden;
}
.vs-show {
  visibility: visible;
}
.fn-left {
  float: left;
}
.fn-right {
  float: right;
}
.bd-ellipsis-oneline {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
  word-wrap: break-word;
}
/*解决font-boosting*/
p {
  max-height: 9999px;
}
/*禁止页面被长按复制
img {
  pointer-events: none;
}*/
img {
  width: 100%;
  height: auto;
  font-size: 0;
}

.font_25{
  font-size: 0.25rem;
}

.font_24{
  font-size: 0.24rem;
}

.font_18{
  font-size: 0.18rem;
}

.font_16{
  font-size: 0.16rem;
}

.font_61{
  font-size: 0.61rem;
}

.font_114{
  font-size: 1.14rem;
}

.font_17{
  font-size: 0.17rem;
}

.font_19{
  font-size: 0.19rem;
}

.font_20{
  font-size: 0.2rem;
}

.font_21{
  font-size: 0.21rem;
}

.font_22{
  font-size: 0.22rem;
}

.font_23{
  font-size: 0.23rem;
}

.font_26{
  font-size: 0.26rem;
}

.font_27{
  font-size: 0.27rem;
}

.font_28{
  font-size: 0.28rem;
}

.font_30 {
  font-size: 0.3rem;
}

.font_32 {
  font-size: 0.32rem;
}

.font_34 {
  font-size: 0.34rem;
}

.font_33 {
  font-size: 0.33rem;
}

.font_35 {
  font-size: 0.35rem;
}

.font_36 {
  font-size: 0.36rem;
}

.font_40 {
  font-size: 0.4rem;
}

.font_44 {
  font-size: 0.44rem;
}

.font_46 {
  font-size: 0.46rem;
}

.font_48 {
  font-size: 0.48rem;
}


.font_37 {
  font-size: 0.37rem;
}

.font_50 {
  font-size: 0.5rem;
}

.font_42 {
  font-size: 0.42rem;
}

.font_37 {
  font-size: 0.37rem;
}

.font_55 {
  font-size: 0.55rem;
}

.font_59 {
  font-size: 0.55rem;
}

.font_100 {
  font-size: 1rem;
}

.color_6600{
  color: #FE6600;
}

.common_text_style{
  font-family: Alibaba PuHuiTi;
  line-height: 0.4rem;
  letter-spacing: 0em;
}
.common_text_style_normal{
  font-family: AlibabaPuHuiTi;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0em;
}

.common_text_style_bold{
  font-family: AlibabaPuHuiTi;
  font-weight: bold;
  line-height: normal;
  letter-spacing: 0em;
}

.common_text_style_normal_28{
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  font-stretch: normal;
  line-height: 0.28rem;
  letter-spacing: 0em;
}

.common_text_style_bold_28{
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: bold;
  font-stretch: normal;
  line-height: 0.28rem;
  letter-spacing: 0em;
}

.flex_center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex_center_baseline {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.flex_column_start{
  display: flex;
  flex-flow: column;
  justify-content: space-between;
  align-items: flex-start;
}

.flex_column {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}


.text_align_center {
  text-align: center;
}

.text_align_left {
  text-align: left;
}

.text_align_right {
  text-align: right;
}


.position-center {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
}

.common_text_style_medium{
  font-weight: 500;
  line-height: 0.4rem;
  letter-spacing: 0;
}

.common_text_common_style{
  font-weight: 400;
  line-height: 0.34rem;
  letter-spacing: 0;
}

.common_text_common_style_44{
  font-weight: 400;
  line-height: 0.44rem;
  letter-spacing: 0;
}

.flex_between_center{
  display: flex;
  align-items: center;
  justify-content: space-between;
}


.color_666666{
  color: #666666;
}

.color_333333{
  color: #333333;
}

.color_999999{
  color: #999999;
}

.color_FD6F38 {
  color: #FD6F38;
}

.color_222222 {
  color: #222222;
}
