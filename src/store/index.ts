import Vue from 'vue'
import Vuex from 'vuex'
import award from './modules/award'
import getters from './getters'
import { IMAGE } from '@/utils/constant'
import music from '@/store/modules/music'
import picture from '@/store/modules/picture'
Vue.use(Vuex)

const store = new Vuex.Store({
  state: {
    IMAGE: IMAGE, // 图片集合
    configData: {}, // 初始化配置数据对象
    url: 'https://oapi.bangdao-tech.com/gateway.do',
    isChecked: false, // 是否选中
    videoIndex: 0, // 视频播放下标
  },
  mutations: {
    CHANGE_STATE: (state: any, payload) => {
      const keys = Object.keys(payload)
      keys.forEach(key => {
        state[key] = payload[key]
      })
    },
    SET_CHECKED: (state: any, payload) => {
      state.isChecked = payload
    },
    SET_VIDEO_INDEX: (state: any, payload) => {
      state.videoIndex = payload
    }
  },
  modules: {
    award,
    music,
    picture,
  },
  getters,
})

export default store
