import Vue from 'vue'
import App from './App.vue'
import router, { loadView } from './router'
import { Route } from 'vue-router'
import store from './store'
import '@/assets/styles/base.less'
import '@/assets/styles/loading.less'
// vant配置
import { Toast, Dialog, Button, Popup ,Field} from 'vant'
import { getImgSize } from '@/utils'
import { Component } from "vue-property-decorator";
Vue.use(Toast).use(Dialog).use(Button).use(Popup).use(Field)
// 设置toast默认配置
Toast.setDefaultOptions({
  forbidClick: true, // 背景不可点击
  overlay: true, // 显示背景遮罩层
})
// 设置toast loading默认配置
Toast.setDefaultOptions('loading', {
  duration: 0, // 不自动关闭
})

Vue.config.productionTip = process.env.NODE_ENV !== 'production'

Component.registerHooks(['beforeRouteEnter', 'beforeRouteLeave'])

// 全局图片处理函数
Vue.prototype.$getImgSize = getImgSize

// 准备json数据和授权操作完成
window.BangdaoSeSdk.ready(() => {
  const _SE_PAGE_MAP = window._SE_PAGE_MAP
  store.commit('CHANGE_STATE', {
    configData: _SE_PAGE_MAP.index.moduleMap.ActCommon.pageTab,
  })
  // 动态设置页面标题
  router.beforeEach((to: Route, from: Route, next) => {
    let pageTitle = ''
    if (to.name) {
      pageTitle = _SE_PAGE_MAP?.[to.name]?.label || ''
    }
    if (!pageTitle && to.meta && to.meta.title) {
      pageTitle = to.meta.title || ''
    }
    document.title = pageTitle
    window._BD_FUNC_setTitle && window._BD_FUNC_setTitle(pageTitle, to.name)
    next()
  })

  // 移除html中的loading
  const loadingEl = document.getElementById('loading')
  if (loadingEl) {
    loadingEl.style.display = 'none'
  }

  // 添加动态路由, 用于页面复制新增
  Object.keys(_SE_PAGE_MAP || {}).forEach(key => {
    const {
      urlPath: path,
      pageId: name,
      component,
      label: title,
      isExtraAdd,
      pageFilePath,
    } = _SE_PAGE_MAP[key]
    if (!isExtraAdd) return
    router.addRoute({
      path,
      name,
      meta: { title },
      component: loadView(component, pageFilePath),
    })
  })

  new Vue({
    router,
    store,
    render: h => h(App),
  }).$mount('#app')
})
