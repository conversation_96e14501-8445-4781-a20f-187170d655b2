import { requestService } from './requestService'
import {
  IGetActivityInfoParams,
  IGetActivityParams,
} from '@/utils/commonInterface'

class activityService extends requestService {
  /**
   * 活动首页查询
   *
   * */
  public async homePage(option): Promise<any> {
    const params = {
      data: {
        ...option,
        method: 'bangdao.gfActivity.home.page',
      },
      handleErr: 'all',
    }
    return this.handleRequest(params)
  }

  /**
   *
   * 活动时间校验接口
   */
  public async activityTime(option): Promise<any> {
    const params = {
      data: {
        ...option,
        method: 'bangdao.gfActivity.verify.activity.time',
      },
      handleErr: 'all',
    }
    return this.handleRequest(params)
  }

  /**
   * 奖品查询接口
   * */
  public async getRecord(option): Promise<any> {
    const params = {
      data: {
        ...option,
        method: 'bangdao.gfActivity.prize.query',
      },
      handleErr: 'all',
    }
    return this.handleRequest(params)
  }

  /**
   *
   * 定位接口
   */
  public async location(option): Promise<any> {
    const params = {
      data: {
        ...option,
        method: 'bangdao.gfActivity.lbs.judge',
      },
      handleErr: 'all',
    }
    return this.handleRequest(params)
  }

  // 发送验证码
  public async sendCode(option): Promise<any> {
    const params = {
      data: {
        ...option,
        method: 'bangdao.gfActivity.ds.sms.send',
      },
      handleErr: 'all',
    }
    return this.handleRequest(params)
  }

  // 登录接口
  public async login(option): Promise<any> {
    const params = {
      data: {
        ...option,
        method: 'bangdao.gfActivity.common.login',
      },
      handleErr: 'all',
    }
    return this.handleRequest(params)
  }

  /**
   * 发奖接口
   *
   */
  public async sendPrize(option): Promise<any> {
    const params = {
      data: {
        ...option,
        method: 'bangdao.gfActivity.award.prize',
      },
      handleErr: 'all',
    }
    return this.handleRequest(params)
  }

  public async getQuestion(option): Promise<any> {
    const params = {
      data: {
        ...option,
        method: 'bangdao.gfActivity.qa.extract',
      },
      handleErr: 'all',
    }
    return this.handleRequest(params)
  }

  /**
   * 微信授权接口
   *
   */
  public async getWX(url): Promise<any> {
    const params = {
      data: {
        method: 'bangdao.lifecycle.wxjsapi.signature',
        url,
        sign_type: 'token',
        // app_id: '2014120100018302',
        app_id: '2015090800259647',
      },
      previewData: {
        ret_code: 200,
        content: {
          rtn_flag: '9999',
          rtn_data: {
            timestamp: '',
            nonce_str: '',
            signature: '',
          },
        },
      },
      gateway: 'https://openapi.bangdao-tech.com/gateway.do',
      handleErr: 'none',
      encrypt: true,
      noLoading: false,
    }
    return this.handleRequest(params)
  }

  /**
   * 提交答题接口
   *
   */
  public async addAnswer(option): Promise<any> {
    const params = {
      data: {
        ...option,
        method: 'bangdao.gfActivity.qa.answer',
      },
      handleErr: 'all',
    }
    return this.handleRequest(params)
  }
   /**
     * 提交接口 （视频或者查看）
     *
  */
  public async addSubmit(options: any) {
    const $options = {
      data: {
        method: "bangdao.gfActivity.submit",
        ...options
      }
    };
    return this.handleRequest($options);
  }
}

export const apis = new activityService()
