/**
 * Mock Manager
 * 接口模拟管理器
 * 
 * 功能：
 * 1. 管理 Service Worker 注册
 * 2. 配置模拟功能开关
 * 3. 管理模拟数据
 * 4. 提供调试接口
 */

export interface MockConfig {
  enabled: boolean
  delay: number
  logRequests: boolean
  mockAll: boolean
  enabledApis: string[]
}

export interface MockData {
  [apiMethod: string]: any
}

class MockManager {
  private serviceWorker: ServiceWorker | null = null
  private config: MockConfig = {
    enabled: false,
    delay: 500,
    logRequests: true,
    mockAll: false,
    enabledApis: []
  }
  private mockData: MockData = {}

  /**
   * 初始化模拟管理器
   */
  async init(): Promise<void> {
    try {
      // 检查浏览器支持
      if (!('serviceWorker' in navigator)) {
        console.warn('[Mock Manager] Service Worker not supported')
        return
      }

      // 加载配置和模拟数据
      await this.loadConfig()
      await this.loadMockData()

      // 注册 Service Worker
      await this.registerServiceWorker()

      // 设置消息监听
      this.setupMessageListener()

      console.log('[Mock Manager] Initialized successfully')
    } catch (error) {
      console.error('[Mock Manager] Initialization failed:', error)
    }
  }

  /**
   * 注册 Service Worker
   */
  private async registerServiceWorker(): Promise<void> {
    try {
      const registration = await navigator.serviceWorker.register('/mock-service-worker.js', {
        scope: '/'
      })

      console.log('[Mock Manager] Service Worker registered:', registration)

      // 等待 Service Worker 激活
      if (registration.installing) {
        await this.waitForServiceWorker(registration.installing)
      } else if (registration.waiting) {
        await this.waitForServiceWorker(registration.waiting)
      } else if (registration.active) {
        this.serviceWorker = registration.active
      }

      // 监听 Service Worker 状态变化
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          this.waitForServiceWorker(newWorker)
        }
      })

      // 发送初始配置
      await this.sendConfigToServiceWorker()
      await this.sendMockDataToServiceWorker()

    } catch (error) {
      console.error('[Mock Manager] Service Worker registration failed:', error)
    }
  }

  /**
   * 等待 Service Worker 激活
   */
  private waitForServiceWorker(worker: ServiceWorker): Promise<void> {
    return new Promise((resolve) => {
      worker.addEventListener('statechange', () => {
        if (worker.state === 'activated') {
          this.serviceWorker = worker
          resolve()
        }
      })
    })
  }

  /**
   * 设置消息监听
   */
  private setupMessageListener(): void {
    navigator.serviceWorker.addEventListener('message', (event) => {
      const { type } = event.data

      switch (type) {
        case 'GET_CONFIG':
          event.ports[0].postMessage({ config: this.config })
          break

        case 'GET_MOCK_DATA':
          event.ports[0].postMessage({ mockData: this.mockData })
          break
      }
    })
  }

  /**
   * 加载配置
   */
  private async loadConfig(): Promise<void> {
    try {
      const savedConfig = localStorage.getItem('mock-config')
      if (savedConfig) {
        this.config = { ...this.config, ...JSON.parse(savedConfig) }
      }
    } catch (error) {
      console.error('[Mock Manager] Failed to load config:', error)
    }
  }

  /**
   * 保存配置
   */
  private saveConfig(): void {
    try {
      localStorage.setItem('mock-config', JSON.stringify(this.config))
    } catch (error) {
      console.error('[Mock Manager] Failed to save config:', error)
    }
  }

  /**
   * 加载模拟数据
   */
  private async loadMockData(): Promise<void> {
    try {
      // 从本地存储加载自定义数据
      const savedMockData = localStorage.getItem('mock-data')
      if (savedMockData) {
        this.mockData = { ...this.mockData, ...JSON.parse(savedMockData) }
      }

      // 加载默认模拟数据
      const response = await fetch('/mock-data.json')
      if (response.ok) {
        const defaultMockData = await response.json()
        this.mockData = { ...defaultMockData, ...this.mockData }
      }
    } catch (error) {
      console.error('[Mock Manager] Failed to load mock data:', error)
    }
  }

  /**
   * 保存模拟数据
   */
  private saveMockData(): void {
    try {
      localStorage.setItem('mock-data', JSON.stringify(this.mockData))
    } catch (error) {
      console.error('[Mock Manager] Failed to save mock data:', error)
    }
  }

  /**
   * 发送配置到 Service Worker
   */
  private async sendConfigToServiceWorker(): Promise<void> {
    if (this.serviceWorker) {
      this.serviceWorker.postMessage({
        type: 'UPDATE_CONFIG',
        data: this.config
      })
    }
  }

  /**
   * 发送模拟数据到 Service Worker
   */
  private async sendMockDataToServiceWorker(): Promise<void> {
    if (this.serviceWorker) {
      this.serviceWorker.postMessage({
        type: 'UPDATE_MOCK_DATA',
        data: this.mockData
      })
    }
  }

  /**
   * 启用模拟功能
   */
  async enable(): Promise<void> {
    this.config.enabled = true
    this.saveConfig()
    await this.sendConfigToServiceWorker()
    console.log('[Mock Manager] Mock enabled')
  }

  /**
   * 禁用模拟功能
   */
  async disable(): Promise<void> {
    this.config.enabled = false
    this.saveConfig()
    await this.sendConfigToServiceWorker()
    console.log('[Mock Manager] Mock disabled')
  }

  /**
   * 设置延迟时间
   */
  async setDelay(delay: number): Promise<void> {
    this.config.delay = delay
    this.saveConfig()
    await this.sendConfigToServiceWorker()
    console.log('[Mock Manager] Delay set to:', delay)
  }

  /**
   * 启用/禁用请求日志
   */
  async setLogRequests(enabled: boolean): Promise<void> {
    this.config.logRequests = enabled
    this.saveConfig()
    await this.sendConfigToServiceWorker()
    console.log('[Mock Manager] Request logging:', enabled ? 'enabled' : 'disabled')
  }

  /**
   * 启用/禁用全部接口模拟
   */
  async setMockAll(enabled: boolean): Promise<void> {
    this.config.mockAll = enabled
    this.saveConfig()
    await this.sendConfigToServiceWorker()
    console.log('[Mock Manager] Mock all APIs:', enabled ? 'enabled' : 'disabled')
  }

  /**
   * 设置启用的接口列表
   */
  async setEnabledApis(apis: string[]): Promise<void> {
    this.config.enabledApis = apis
    this.saveConfig()
    await this.sendConfigToServiceWorker()
    console.log('[Mock Manager] Enabled APIs updated:', apis)
  }

  /**
   * 添加启用的接口
   */
  async addEnabledApi(api: string): Promise<void> {
    if (!this.config.enabledApis.includes(api)) {
      this.config.enabledApis.push(api)
      await this.setEnabledApis(this.config.enabledApis)
    }
  }

  /**
   * 移除启用的接口
   */
  async removeEnabledApi(api: string): Promise<void> {
    const index = this.config.enabledApis.indexOf(api)
    if (index > -1) {
      this.config.enabledApis.splice(index, 1)
      await this.setEnabledApis(this.config.enabledApis)
    }
  }

  /**
   * 设置模拟数据
   */
  async setMockData(apiMethod: string, data: any): Promise<void> {
    this.mockData[apiMethod] = data
    this.saveMockData()
    await this.sendMockDataToServiceWorker()
    console.log('[Mock Manager] Mock data updated for:', apiMethod)
  }

  /**
   * 获取模拟数据
   */
  getMockData(apiMethod?: string): any {
    if (apiMethod) {
      return this.mockData[apiMethod]
    }
    return this.mockData
  }

  /**
   * 删除模拟数据
   */
  async removeMockData(apiMethod: string): Promise<void> {
    delete this.mockData[apiMethod]
    this.saveMockData()
    await this.sendMockDataToServiceWorker()
    console.log('[Mock Manager] Mock data removed for:', apiMethod)
  }

  /**
   * 获取当前配置
   */
  getConfig(): MockConfig {
    return { ...this.config }
  }

  /**
   * 获取状态
   */
  async getStatus(): Promise<any> {
    if (!this.serviceWorker) {
      return { enabled: false, error: 'Service Worker not available' }
    }

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel()
      messageChannel.port1.onmessage = (event) => {
        resolve(event.data)
      }

      this.serviceWorker!.postMessage({
        type: 'GET_STATUS'
      }, [messageChannel.port2])
    })
  }

  /**
   * 清除缓存
   */
  async clearCache(): Promise<void> {
    if (this.serviceWorker) {
      this.serviceWorker.postMessage({ type: 'CLEAR_CACHE' })
      console.log('[Mock Manager] Cache cleared')
    }
  }

  /**
   * 重置所有配置
   */
  async reset(): Promise<void> {
    localStorage.removeItem('mock-config')
    localStorage.removeItem('mock-data')
    await this.loadConfig()
    await this.loadMockData()
    await this.sendConfigToServiceWorker()
    await this.sendMockDataToServiceWorker()
    console.log('[Mock Manager] Reset to default configuration')
  }
}

// 创建全局实例
export const mockManager = new MockManager()

// 开发环境下自动初始化
if (process.env.NODE_ENV === 'development') {
  mockManager.init().catch(console.error)
}

// 全局调试接口
if (typeof window !== 'undefined') {
  (window as any).mockManager = mockManager
}
