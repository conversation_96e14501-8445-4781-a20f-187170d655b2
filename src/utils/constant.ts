export const IMAGE = {
  CLOSE:
    'https://psm.bangdao-tech.com/interaction-putting/20316/img/20230807171823798100103029104652_w62_h62.png',
  BACK:
    'https://psm.bangdao-tech.com/interaction-putting/20316/img/20230808100136041100103030504655_w50_h50.png', //  返回按钮
}

// 测试环境/本地调试
export const isTestEnv =
  window.location.hostname.indexOf('test') > -1 ||
  /^((localhost)|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))$/.test(
    window.location.hostname
  ) ||
  /query_env=TEST/.test(window.location.href)

// 不调接口
export const noRequest =
  (window._SE_ACT_TEMPLATE_ENV === 'local' &&
    /authToken=test/.test(window.location.href)) ||
  window._SE_ACT_TEMPLATE_ENV === 'editor'

export const gateway = isTestEnv
  ? 'https://test-napi.bangdao-tech.com/gateway.do'
  : 'https://napi.bangdao-tech.com/gateway.do'
// : 'https://oapi.bangdao-tech.com/gateway.do'
const userAgent = navigator.userAgent
const isWX =
  window._SE_ACT_TEMPLATE_ENV === 'local' ||
  userAgent.indexOf('MicroMessenger') > -1
const isAli = userAgent.indexOf('AlipayClient') > -1
export const platform = isWX ? 'WECHAT' : isAli ? 'ALIPAY' : 'OTHER'

//判断是否是国网环境
export const isNational = userAgent.indexOf('wsgw') > -1

export const isWin = isWX


/**
 * 获取url透传参数
 */
export const getRequest = (key): any => {
  const paramStr = window.location.search
  const regExp = new RegExp('(^|\\?|&)'.concat(key, '=([^&]*)(\\s|&|$)'))
  return regExp.test(paramStr) ? RegExp.$2 : ''
}

//ios 环境
const ua = navigator.userAgent
export function isiOS() {
  return !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
}

/**
 * ios签名兼容性
 */
export const getSignUrl = () => {
  let signLink = ''
  if (isiOS()) {
    // @ts-ignore
    signLink = localStorage.getItem('signUrl')
    if (!signLink) signLink = window.location.href.split('#')[0]
  } else {
    signLink = window.location.href.split('#')[0]
  }
  return signLink
}

/**
 * 微信分享中转路径
 */
export enum SHARE {
  URL = 'https://psm.bangdao-tech.com/interaction-putting/20316/published/PL66490140516367/index.html#/share?shareUrl=000000',
}

export enum btnStatus {
  complete = 'linear-gradient(90deg, #fece5a 0%, #fd6a0e 100%), linear-gradient(#ffffff, #ffffff)',
  unComplete = 'linear-gradient(90deg, #fece5a 0%, #fd6a0e 100%), linear-gradient(#ffffff, #ffffff)',
}
