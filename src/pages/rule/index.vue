<template>
  <div class="link">
    <backButton
      :isCover="isCover"
      :bgColor="bgColor"
      :back-icon="IMAGE.BACK"
      :path-name="'index'"
    ></backButton>
    <iframe
      :style="{
        minHeight: 'calc(100vh - ' + `${isCover ? '0' : '0.84'}` + 'rem)',
      }"
      width="100%"
      frameborder="0"
      :src="url"
    ></iframe>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import InitMask from '@/components/mask/InitMask.vue'
import Background from '@/components/background/Background.vue'
import BackButton from '@/components/IframeBack/index.vue'
import { State } from 'vuex-class'

@Component({
  components: {
    InitMask,
    Background,
    BackButton,
  },
})
export default class ActCommon extends Vue {
  @State('IMAGE') IMAGE
  /**
   * 组件本身用到的状态
   */

  url: any = '' // 链接
  isCover = false
  bgColor: any = ''

  mounted() {
    // 配置项
    console.log('链接:', this.$route.query.url)
    const { isCover, bgColor, url } = this.$route.query
    this.isCover = isCover === 'T'
    this.bgColor = bgColor
    this.url = url
  }
}
</script>
<style lang="less" scoped></style>
