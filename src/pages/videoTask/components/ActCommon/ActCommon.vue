<template>
  <Background v-bind="bgLayout">
    <InitMask v-if="!isReady" :loadingColor="pageTab.bgColor"></InitMask>
    <back-button></back-button>
    <div class="content">
      <div class="title font_26 flex_column">{{pageTab.titleContent}}</div>
      <div class="video">
        <video
          webkit-playsinline
          playsinline
          ref="video"
          autoplay
          preload="auto"
          :src="videoUrl"
          controls
        ></video>
      </div>
    </div>
    <!-- 退出弹窗 -->
    <ExitDialog
      v-model="showExit"
      :dialogBg="pageTab.imgList.exitDialogBg"
      :content="pageTab.exitDialogContent"
      @stopTimer="stopTimer"
      @continueExit="continueExit"
    ></ExitDialog>
    <answer-other-modal ref="answerOtherRef" :page-tab="pageTab" @jumpOperation="continueLevel" @backOperation="backChallenge"></answer-other-modal>
  </Background>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref } from 'vue-property-decorator'
import InitMask from '@/components/mask/InitMask.vue'
import { imageLoadedHandle, preLoadImage } from '@/utils'
import { apis } from '@/utils/service/apis'
import { buriedPoint } from '@/utils/common'
import ExitDialog from './ExitDialog.vue'
import { showErrorMsg } from '@/utils/showErrorMsg'
import Background from '@/components/background/Background.vue'

import { jumpTime } from '@/utils/debounce'
import BackButton from '@/components/back/BackButton.vue'
import AnswerOtherModal from '@/components/answerChallengeModal/answerOtherModal.vue'
import { State } from "vuex-class";

@Component({
  computed: {},
  components: {
    AnswerOtherModal,
    BackButton,
    InitMask,
    ExitDialog,
    Background,
  },
})
export default class Answer extends Vue {
  // 页面基础配置
  @Prop({
    type: Object,
  })
  pageTab

  isReady = false

  showExit = false
  done = false
  exitConfirmVisible = false
  exitConfirmResult: any = null

  videoList:any = []

  @Ref('answerOtherRef') answerOtherRef: any

  @Ref('video') videoRef;

  @State('configData') configData: any
  @State('videoIndex') videoIndex: any

  // 页面背景和布局配置
  get bgLayout() {
    const { layoutType, bgImage, subBgList, bgColor } = this.pageTab
    return {
      layoutType,
      bgImage: [
        bgImage,
        ...subBgList.map(item => item.image).filter(item => !!item),
      ],
      bgColor,
      isHandleTransparentTitle: true,
    }
  }

  videoUrl :any= null;

  playCallBack:any = null;


  isComplete = false;




  protected async created() {
    this.videoList = this.pageTab.videoList;
    //图片加载完成执行callback
    await imageLoadedHandle([this.pageTab.bgImage], async () => {
      // await this.getQuestions()
      this.isReady = true
    })
    preLoadImage(Object.values(this.pageTab.imgList))
  }

  mounted() {
    this.videoUrl = this.videoList[this.videoIndex]?.videoUrl;
    if(!this.videoUrl) return;
    this.playCallBack = () => {
      this.playBack();
    };
    this.$nextTick(() => {
      this.videoRef.load();
      this.videoRef.addEventListener('ended', this.playCallBack, false);
    });
  }


  async playBack(){
    await this.finishTask();
  }

  async finishTask() {
    const [res, err] = await apis.addSubmit({
      activity_id: this.pageTab.activityId,
      app_id: this.pageTab.appId,
      pubms_code: this.pageTab.pubms_code,
      item_type: this.pageTab.item_type,
      token: localStorage.getItem(this.configData.storageName),
      append_type : 'VEDIO'
    });
    this.done = true
    if (err) {
      const { rtn_flag, rtn_msg, rtn_data } = err
      const describe = this.pageTab.errorList.find(item => item.code === rtn_flag)?.codeText
      return this.$toast(describe ?? rtn_msg ?? '系统繁忙，请稍候')
    }
    if(res) {
      const {rtn_data :{isComplete}} = res;
      this.isComplete = isComplete;
      this.answerOtherRef.open({content:"恭喜获得1次闯关机会",leftText:'返回首页',rightText:'继续闯关'})
    }
  }

  destroyed() {
    this.videoRef && this.videoRef.removeEventListener('ended', this.playCallBack);
    this.videoRef && this.videoRef.pause();
  }


  public exitConfirm() {
    console.log('====🚀====this.done=====🚀=====', this.done)
    if (this.done) {
      return Promise.resolve(true)
    }
    this.exitConfirmResult = null
    this.exitConfirmVisible = true
    this.showExit = true
    this.videoRef && this.videoRef.pause();

    return new Promise(resolve => {
      const unwatch = this.$watch(
        () => this.exitConfirmResult,
        value => {
          // 停止监听
          unwatch()

          // 清空状态
          this.exitConfirmResult = null
          this.exitConfirmVisible = false
          this.showExit = false

          // 返回结果
          resolve(value)
        }
      )
    })
  }

  stopTimer() {
    buriedPoint('视频页面-确认退出')
    this.done = true
    this.exitConfirmResult = true
  }


  continueExit() {
    buriedPoint('视频页面-继续观看')
    this.showExit = false
    this.exitConfirmResult = false
    this.videoRef && this.videoRef.play();
  }

  continueLevel() {
    buriedPoint('继续挑战')
    this.done = true
    this.$router.replace({ path: this.isComplete ? '/':'/answer' })
  }

  backChallenge() {
    buriedPoint('返回首页')
    this.done = true
    this.$router.replace({ path: '/' })
  }
}
</script>

<style lang="less" scoped>
.content{
  padding: 0 0.2rem;
}
.title{
  position: absolute;
  top:0.5rem;
  //left: 0.2rem;
  width: 7.1rem;
  height: 0.8rem;
  background-image: linear-gradient(90deg, #def6fe 0%, #e6fecd 100%), linear-gradient(#ffffff, #ffffff);
  background-blend-mode: normal, normal;
  border-radius: 0.2rem;
  border: solid 1px #a8d0da;
  line-height: 0.34rem;
  color: #2b5e9f;
}
.video{
  margin-top: 1.6rem;
}
video {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
