<template>
  <transition name="dialog-transition">
    <div v-if="show" class="page-mask" @touchmove.prevent>
      <div
        class="dialog-container"
        :style="{
          backgroundImage: `url(${dialogBg})`,
        }"
      >
        <div class='content flex_column'>
          <div class="dialog-content" v-html="content" ></div>
        </div>
        <div class="dialog-button">
          <div class="left-button" @click="$emit('stopTimer')"></div>
          <div class="right-button" @click="$emit('continueExit')"></div>
        </div>
        <div class="dialog-close" @click="$emit('continueExit')"></div>
      </div>
    </div>
  </transition>
</template>

<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator'
import { buriedPoint } from '@/utils/common'
@Component
export default class LoginPop extends Vue {
  @Model('change', { type: Boolean, default: false }) readonly show!: boolean
  // 弹窗展示禁止背景滚动的兼容处理，记录当前滚动位置
  private top = 0;
  // 固定视窗，禁止滚动
  @Watch('show')
  onShowChanged(val: boolean) {
    if (window._SE_ACT_TEMPLATE_ENV === "editor") return;
    //兼容处理，弹窗出现背景可滚动的场景
    const $mainPage = document.querySelector("#app") as HTMLElement;
    if (val) {
      buriedPoint('退出答题提示弹窗')
      this.top = window.scrollY;
      $mainPage.style.position = "fixed";
      $mainPage.style.top = -this.top + "px";
    } else {
      $mainPage.style.position = "";
      $mainPage.style.top = "";
      $mainPage.style.zIndex = "1";
      // 回到原先的top
      window.scrollTo(0, this.top);
    }
  }
  @Prop({ type: String, default: '' }) dialogBg: string | undefined
  @Prop({ type: String, default: '' }) content: string | undefined
}
</script>
<style lang="less" scoped>
.dialog-transition-enter-active,
.dialog-transition-leave-active {
  transition: all 0.3s;
}
.dialog-transition-enter,
.dialog-transition-leave-to {
  opacity: 0;
  transform: translate3d(0, 0, 0);
}
.page-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1999;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  overflow: hidden;
  .dialog-container {
    position: absolute;
    left: 14%;
    transform: translateX(-50%);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 5.6rem;
    height: 4.33rem;
    box-sizing: border-box;
    // 卡片弹出动画
    animation: dialog 0.35s ease-in forwards;
    transform-origin: center center;
    @keyframes dialog {
      0% {
        transform: scale(0);
        top: 7rem;
      }
      100% {
        transform: scale(1);
        top: 5rem;
      }
    }

    .content {
      position: absolute;
      top:1.33rem;
      //left:0.51rem;
      width: 100%;
      .dialog-content {
        font-size: 0.3rem;
        font-weight: normal;
        line-height: 0.4rem;
        text-align: center;
        color: #12253a;
      }
    }


    .dialog-button {
      height: 0.82rem;
      width: 4.64rem;
      position: absolute;
      left: 0.5rem;
      top: 2.93rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left-button {
        width: 2.12rem;
        height: 0.82rem;
        background-image: url('https://psm.bangdao-tech.com/interaction-putting/20316/img/20250407105611326099058126500039_w212_h82.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }
      .right-button {
        width: 2.12rem;
        height: 0.82rem;
        background-image: url('https://psm.bangdao-tech.com/interaction-putting/20316/img/20250407170856727099058127203334_w212_h82.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }
    }
  }
  .dialog-close {
    width: 0.21rem;
    height: 0.21rem;
    position: absolute;
    top: 0.12rem;
    right: 0.2rem;
  }
}
</style>
