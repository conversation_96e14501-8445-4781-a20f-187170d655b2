Object(页面配置) {
  pageTab(基础配置): Tab {
    bgImage(背景图片): Image [tip: '尺寸:750*1496+；背景过长时，可以将背景切分，非首屏展示区的背景图片配置在"延伸背景"中'],
    bgColor(背景颜色): Color,
    layoutType(页面布局): Enum {
      onePageTop(一屏展示【背景图片:750*1496】),
      imageSizeTop(图长可滚动【背景图片:750*任意】)
    },
    subBgList(延伸背景): Array [tip: 尺寸：750*任意，长图背景可切分非首屏的背景配置在此处，加快首页渲染, rely: $layoutType$ === 'imageSizeTop'] {
      image(背景图片): Image
    },
    activityId(活动Id): String,
    appId(appId): String,
    source(渠道来源): String,
    item_type(类型): String,
    pubms_code(机构码): String,
    isClose(是否关闭): Boolean,
    showConsole(是否显示控制台): Boolean,
    token(测试token): String,
    storageName(本地存储名称): String,
    linkUrl(下载链接): Link,
    registerModalImage(注册弹窗背景): Image,
    registerConfirmImage(注册确认弹窗背景): Image,
    lottieJson(动画JSON): String,

    textConfig(文案配置): Object {
      title(标题): String [tip: '支持<br/>换行'],
      signTitle(报名标题): String [tip: '支持<br/>换行'],
      timesTitle(次数标题): String [tip: '支持<br/>换行'],
      passingTitle(通关标题): String [tip: '支持<br/>换行'],
      btnText(按钮文案): String,
      passTitle(通关完成标题): String [tip: '支持<br/>换行']
    },

    taskList(任务列表): Array {
      taskName(任务名称): String,
      remark(任务说明): String,
      describe(任务描述): String,
      completionStatus(完成状态): Number,
      taskType(任务类型): String
    },

    imgList(图片配置): Object {
      arrow(箭头图片): Image,
      completedLock(已完成锁): Image,
      lockedLock(锁定图片): Image,
      currentLock(当前锁): Image,
      challengeBtn(挑战按钮): Image,
      challengeBtnPass(通关按钮): Image,
      dialogBg(弹窗背景): Image,
      dialogOtherBg(其他弹窗背景): Image,
      answerCommonBg(答题通用背景): Image,
      guide(引导图片): Image,
      guideTitle(引导标题): Image,
      secondGuideTitle(二级引导标题): Image,
      guideBtn(引导按钮): Image
    },

    dataList(热区配置): Array {
      brief(描述): String,
      name(名称): String,
      position(位置信息): Object {
        top(顶部距离): Number,
        left(左侧距离): Number,
        right(右侧距离): Number,
        width(宽度): Number,
        height(高度): Number
      },
      url(跳转链接): String,
      handle(处理方式): String
    },

    errorList(错误配置): Array {
      codeText(错误描述): String [tip: '支持<br/>换行'],
      code(错误码): String,
      type(错误类型): String,
      isShow(是否显示): Boolean
    },

    chanceErrorList(机会错误配置): Array {
      codeText(错误描述): String,
      code(错误码): String
    }
  },
  shareInfo(分享配置): Tab {
    title(分享标题): String,
    content(分享内容): String,
    iconUrl(分享图标): Image,
    shareUrl(分享链接): Link,
    zhiInTitle(知行内标题): String,
    zhiContent(知行内容): String,
    zhiLBtnText(左按钮文案): String,
    zhiRBtnText(右按钮文案): String,
    zhiList(知行列表): Array {
      zhiPreContent(前置内容): String,
      zhiEndContent(后置内容): String
    }
  }
}
