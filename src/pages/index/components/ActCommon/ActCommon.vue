<template>
  <Background v-bind="bgLayout">
    <InitMask v-if="!isReady" :loadingColor="pageTab.bgColor"></InitMask>
<!--    <back-button></back-button>-->
    <div class="hot-area">
      <div
        class="hot-area-list"
        v-for="(item, index) in pageTab.dataList"
        :key="index"
        :style="hotAreaList(item)"
        @click="itemClickHandle(item)"
      ></div>
    </div>
    <div class="challenge-progress">
      <div class="progress-container">
        <div
          v-for="(item, index) in challengeSteps"
          :key="index"
          class="step-item"
          :class="{
            active: currentStep >= index + 1,
            current: currentStep === index + 1,
          }"
        >
          <div class="step-top">
            <div
              class="step-icon"
              :style="getImage(getStepIcon(item, index))"
            ></div>
            <div class="step-name">{{ item.name }}</div>
          </div>
          <div
            class="step-arrow"
            v-if="index < challengeSteps.length - 1"
            :style="getImage(pageTab.imgList.arrow)"
          ></div>
        </div>
      </div>
    </div>
    <div class="challenge-content flex_column">
      <div
        class="challenge-content__btn"
        :style="getImage(btnImg)"
        @click="handleChallenge"
      ></div>
      <div class="challenge-content__title font_28 ">
        您还有{{ answerTimes }}次闯关机会
      </div>
    </div>
    <task-area :task-list="taskList" @handleTask="handleTask"></task-area>
    <!--  异常情况和登录   -->
    <err-modal ref="modalRef"></err-modal>
    <answer-tips-modal
      ref="tipsModalRef"
      @jumpOperation="jumpOperation"
      :page-tab="pageTab"
    ></answer-tips-modal>
    <modal
      ref="modalLottieRef"
      class="lottie-modal"
      :show-close-button="false"
      :bg-obj="{ background: '' }"
    >
      <div class="lottie-container" id="lottieContainer"></div>
      <div class="lottie-btn" @click="handleLottie"></div>
    </modal>
    <modal
      ref="modalShareRef"
      :show-close-button="false"
      :bg-obj="{ background: '' }"
    >
      <div class="share-modal">
        <div class="guide" :style="getImage(pageTab.imgList.guide)"></div>
        <div
          class="guide-title"
          :style="getImage(pageTab.imgList.guideTitle)"
        ></div>
        <div
          class="guide-second-title"
          :style="getImage(pageTab.imgList.secondGuideTitle)"
        ></div>
        <div
          class="share-btn"
          :style="getImage(pageTab.imgList.guideBtn)"
          @click="modalShareRef.show = false"
        ></div>
      </div>
    </modal>
  </Background>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref } from 'vue-property-decorator'
import {
  buriedPoint,
  getImgSize,
  imageLoadedHandle,
  preLoadImage,
} from '@/utils'
import InitMask from '@/components/mask/InitMask.vue'
import Background from '@/components/background/Background.vue'
import { apis } from '@/utils/service/apis'
import { jumpTime } from '@/utils/debounce'
import BackButton from '@/components/back/BackButton.vue'
import lottie from 'lottie-web'
import TaskArea from '@/pages/index/components/ActCommon/TaskArea.vue'
import Modal from '@/components/modal/Modal.vue'
import ErrModal from '@/components/modal/errModal.vue'
import AnswerTipsModal from '@/components/answerChallengeModal/answerTipsModal.vue'
import { Mutation, namespace } from "vuex-class";
import { isiOS } from "@/utils/constant";

const PicStore = namespace('picture')

@Component({
  methods: { getImgSize },
  components: {
    AnswerTipsModal,
    ErrModal,
    Modal,
    TaskArea,
    BackButton,
    InitMask,
    Background,
  },
})
export default class ActCommon extends Vue {
  // 页面维度配置
  @Prop({
    type: Object,
    default: () => {
      return {}
    },
  })
  pageConfig
  // 页面基础配置
  @Prop({
    type: Object,
    default: () => {
      return {}
    },
  })
  pageTab

  @Prop({
    type: Object,
    default: {},
  })
  private shareInfo: any

  // 页面背景和布局配置
  get bgLayout() {
    const { layoutType, bgImage, subBgList, bgColor } = this.pageTab
    return {
      layoutType,
      bgImage: [
        bgImage,
        ...subBgList.map(item => item.image).filter(item => !!item),
      ],
      bgColor,
      isHandleTransparentTitle: true,
      transparentTitleConfig: this.pageConfig?.transparentTitleConfig,
    }
  }

  /**
   * 组件本身用到的状态
   */
  // 页面所需图片的加载完成
  isReady = false

  // 答题挑战关卡数据
  challengeSteps = [
    { name: '第一关', status: 'locked' },
    { name: '第二关', status: 'locked' },
    { name: '第三关', status: 'locked' },
    { name: '第四关', status: 'locked' },
    { name: '第五关', status: 'locked' },
  ]

  // 当前进行到的关卡
  currentStep = 1

  showPass = false //是否通关

  answerTimes = 0 //答题次数

  private animation: any // 动效

  taskList: any = []

  @Ref('modalRef') modalRef: any
  @Ref('tipsModalRef') tipsModalRef: any
  @Ref('modalLottieRef') modalLottieRef: any
  @Ref('modalShareRef') modalShareRef: any

  @Mutation('SET_VIDEO_INDEX') setVideoIndex: any

  describe = ''

  /**
   *
   * 微信分享
   */
  @PicStore.Action('resetWXShare') resetWXShare: any
  @PicStore.Mutation('setShareCfg') setShareCfg: any

  protected async created() {
    // isiOS() && localStorage.setItem('signUrl', window.location.href.split('#')[0])
    await this.initShare()
    this.taskList = this.pageTab.taskList
    //图片加载完成执行callback
    await imageLoadedHandle([this.pageTab.bgImage], () => {
      this.verifyTime()
      this.isReady = true
    })
    // 图片预加载
    preLoadImage(Object.values(this.pageTab.imgList))
  }

  mounted() {
    // this.showSignModal();
    // const rtn_flag = '04011008'
    // this.describe = this.pageTab.errorList.find(
    //   item => item.code === rtn_flag
    // )?.codeText
    // if (this.describe && (rtn_flag === '04011007' || rtn_flag === '04011008')) {
    //   this.modalRef.open({ describe: this.describe, isVerifyTime: true })
    //   return
    // }
    // if (this.describe) {
    //   this.modalRef.open({ describe: this.describe, isVerifyTime: false })
    //   return
    // }
    // this.tipsModalRef.open({
    //   title: '温馨提示',
    //   content: this.pageTab.textConfig.passingTitle,
    //   btnText: '立即闯关',
    //   type: 'passing',
    // })
    // this.tipsModalRef.open({title:'',content:'当前活动需要完成e起节电报名。</br>如您已完成报名，请您明天来参与</br>活动赢好礼哦~',btnText:'立即报名',type:'sign'})
    // this.showComplete();
    // this.modalShareRef.show = true
  }

  /**
   *  设置微信分享
   *
   */
  async initShare() {
    // const { title, content, iconUrl, shareUrl } = shareInfo
    console.log('====🚀===this.shareInfo======🚀=====', this.shareInfo)
    this.setShareCfg(this.shareInfo)
    this.resetWXShare()
  }

  /**
   * 方法区
   *
   */
  getImage(imgUrl: string) {
    return {
      backgroundImage: `url(${imgUrl})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      ...getImgSize(imgUrl),
    }
  }

  get btnImg() {
    return this.showPass
      ? this.pageTab.imgList.challengeBtnPass
      : this.pageTab.imgList.challengeBtn
  }

  showComplete() {
    this.modalLottieRef.show = true
    setTimeout(() => {
      this.initLottie(this.pageTab.lottieJson, 'lottieContainer', false)
    })
  }

  @jumpTime()
  handleLottie() {
    buriedPoint('_点击查看奖励')
    this.modalLottieRef.show = false
    setTimeout(() => {
      this.$router.push({ path: '/myAward' })
    })
  }

  showCommonModal() {
    this.tipsModalRef.open({
      title: '',
      content: this.pageTab.textConfig.title,
      btnText: '立即报名',
      type: 'sign',
    })
  }

  /**
   * 获取步骤图标
   */
  getStepIcon(item, index) {
    // 当前步骤之前的步骤显示已解锁图标（橙色）
    if (this.currentStep > index + 1 || this.showPass) {
      return this.pageTab.imgList.completedLock
    }
    // 当前步骤显示当前图标（蓝色）
    else if (this.currentStep === index + 1) {
      return this.pageTab.imgList.currentLock
    }
    // 当前步骤之后的步骤显示锁定图标（灰色）
    else {
      return this.pageTab.imgList.lockedLock
    }
  }

  /**
   * 完成任务/领取接口
   * 任务类型（2视频观看，3每日交费）
   */
  @jumpTime()
  async finishTask(item) {
    const { completionStatus, id, taskType } = item
    const [, err] = await apis.addSubmit({
      activity_id: this.pageTab.activityId,
      app_id: this.pageTab.appId,
      pubms_code: this.pageTab.pubms_code,
      item_type: this.pageTab.item_type,
      token: localStorage.getItem(this.pageTab.storageName),
      append_type : ''
    });
    if (err) {
      return this.showErr(err);
    }
    if(taskType == '3') {
      await this.homePage();
      this.scrollToTop()
      setTimeout(() => {
        this.modalShareRef.show = true
      }, 1000);
    }
  }

  scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    });
  }

  @jumpTime()
  async handleTask(item) {
    if (item.completionStatus === 2) return
    const taskConfig = {
      2: () => {
        buriedPoint('点击去报名')
        window.location.href = this.pageTab.linkUrl
      },
      4: () => {
        buriedPoint('点击查看节电视频任务')
        this.$router.push({
          path: '/taskVideo'
        })
      },
      3: () => {
        buriedPoint('点击查看转发活动')
        this.finishTask(item)
      },
    }
    taskConfig[item.taskType]()
  }

  jumpOperation(type) {
    if(type === 'passing') return  this.$router.push({path: '/answer'})
  }

  @jumpTime()
  handleChallenge() {
    buriedPoint('开始闯关')
    if(this.showPass ) return this.tipsModalRef.open({
      title: '温馨提示',
      content: this.pageTab.textConfig.passTitle,
      btnText: '我知道了',
      type: '',
    });
    if (this.answerTimes <= 0)
      return this.tipsModalRef.open({
        title: '温馨提示',
        content: this.pageTab.textConfig.timesTitle,
        btnText: '我知道了',
        type: '',
      })
    this.tipsModalRef.open({
      title: '温馨提示',
      content: this.pageTab.textConfig.passingTitle,
      btnText: '立即闯关',
      type: 'passing',
    })
  }

  // 动画设置
  private initLottie(data: any, eleId: string, loop = true) {
    const _id = document.getElementById(eleId)
    if (_id) {
      this.animation = lottie.loadAnimation({
        container: _id, // 当前需要渲染的DOM
        renderer: 'svg',
        loop, // 是否循环播放
        autoplay: true, // 是否自动播放
        path: data,
      })
    }
  }

  /**
   * 方法区
   *
   */

  /**
   *
   * 活动时间校验接口
   */
  async verifyTime() {
    this.$toast.loading({
      forbidClick: true,
    })
    const [res, err] = await apis.activityTime({
      activity_id: this.pageTab.activityId,
      app_id: this.pageTab.appId,
      token: localStorage.getItem(this.pageTab.storageName),
    })
    if (err) {
      this.$toast.clear()
      return this.showErr(err)
    }
    if (res) {
      await this.homePage()
    }
  }

  showErr(err) {
    const { rtn_flag, rtn_msg, rtn_data } = err
    this.describe = this.pageTab.errorList.find(
      item => item.code === rtn_flag
    )?.codeText
    if (this.describe && (rtn_flag === '04011007' || rtn_flag === '04011008')) {
      this.modalRef.open({
        describe: this.describe,
        isVerifyTime: true,
        isTimeLabel: true,
      })
      return
    }
    if (this.describe) {
      this.modalRef.open({ describe: this.describe, isVerifyTime: true })
      return
    }
    return this.$toast(this.describe ?? rtn_msg ?? '系统繁忙，请稍候')
  }

  /**
   * 首页接口
   *
   */
  async homePage() {
    this.$toast.loading({
      forbidClick: true,
    })
    const [res, err] = await apis.homePage({
      activity_id: this.pageTab.activityId,
      app_id: this.pageTab.appId,
      pubms_code: this.pageTab.pubms_code,
      item_type: this.pageTab.item_type,
      token: localStorage.getItem(this.pageTab.storageName),
      // extend_params: this.pageTab.isClose ? { lng: "116.401768", lat: "39.918884" } : {}
    })
    if (err) {
      this.$toast.clear()
      return this.showErr(err)
    }
    if (res) {
      const {
        rtn_data: {
          isComplete,
          isEnroll,
          isShare,
          isVedio,
          level,
          qaNum,
          vedioNum,
          isLogin,
        },
      } = res
      this.answerTimes = qaNum
      this.currentStep = level
      this.showPass = isComplete
      this.setVideoIndex(vedioNum)
      this.initLevel(level)
      this.initTask({ isShare, isVedio, isEnroll, vedioNum, isLogin })
    }
  }

  /**
   *
   * 初始化任务
   * @param task 任务
   */
  initTask(task: any) {
    const { isShare, isLogin, isVedio, isEnroll, vedioNum } = task

    this.taskList = this.taskList.map(item => {
      switch (item.taskType) {
        case '1': // 报名任务
          return {
            ...item,
            completionStatus: isLogin ? 2 : 0, // 2-已完成已领取 0-未完成
          }
        case '2': // 查看任务
          return {
            ...item,
            completionStatus: isEnroll ? 2 : 0, // 2-已完成已领取 0-未完成
          }
        case '3': // 分享任务
          return {
            ...item,
            completionStatus: isShare ? 2 : 0, // 2-已完成已领取 0-未完成
          }
        case '4': // 视频任务
          return {
            ...item,
            taskNum: vedioNum,
            completionStatus: isVedio ? 2 : 0, // 2-已完成已领取 1-已完成未领取
          }
        default:
          return item
      }
    })
  }

  /**
   *
   * 初始化关卡
   * @param level 关卡
   */
  initLevel(level: any) {
    this.challengeSteps = this.challengeSteps.map((step, index) => {
      if (index + 1 < level) {
        // 当前关卡之前的关卡 - 已完成
        return { ...step, status: 'completed' }
      } else if (index + 1 === level) {
        // 当前关卡 - 进行中
        return { ...step, status: 'current' }
      } else {
        // 当前关卡之后的关卡 - 锁定
        return { ...step, status: 'locked' }
      }
    })
  }

  /**
   * 热区位置样式
   *
   */
  hotAreaList(item) {
    const { position } = item
    return {
      right: `${position.right / 100}rem`,
      left:  `${position.left / 100}rem` ,
      top: `${position.top / 100}rem`,
      width: `${position.width / 100}rem`,
      height: `${position.height / 100}rem`,
      position: 'absolute',
    }
  }

  /**
   *
   * 热区点击事件
   */
  itemClickHandle(item) {
    buriedPoint(item.brief)
    if (item.name === 'rule' || item.name === 'service') {
      this.$router.replace({
        path: '/rule',
        query: {
          url: item.url,
          isCover: item.name === 'service' ? 'T' : '',
        },
      })
    } else {
      this.$router.push({
        path: '/myAward',
      })
    }
  }
}
</script>
<style lang="less" scoped>
.challenge-content {
  position: absolute;
  top: 11.39rem;
  left: 0;
  width: 100%;
  margin: 0 auto;

  &__title {
    margin-top: 0.19rem;
    line-height: 0.34rem;
    color: #2b5e9f;
  }
}

.challenge-progress {
  position: absolute;
  top: 9.09rem;
  left: 0.7rem;

  .progress-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;

    .step-item {
      display: flex;
      justify-content: center;
      align-items: center;

      .step-top {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 0.83rem;
      }

      .step-icon {
        width: 0.8rem;
        height: 0.8rem;
      }

      .step-arrow {
        margin: -0.3rem 0.12rem 0;
      }

      .step-name {
        font-size: 0.24rem;
        color: #12253a;
        margin-top: 0.1rem;
        text-align: center;
      }

      //&.active {
      //  .step-name {
      //    color: #FF6E30;
      //    font-weight: bold;
      //  }
      //}
      //
      //&.current {
      //  .step-name {
      //    color: #3B9AFF;
      //    font-weight: bold;
      //  }
      //}
    }
  }
}

.lottie-modal {
  .lottie-container {
    width: 7.5rem;
    //height: 4rem;
  }

  .lottie-btn {
    position: absolute;
    top: 11rem;
    left: 2.3rem;
    background: url('https://psm.bangdao-tech.com/interaction-putting/20316/img/20250407151046647099058127003640_w312_h83.png')
      no-repeat;
    background-size: 100% 100%;
    width: 3.12rem;
    height: 0.83rem;
  }
}

.share-modal {
  position: absolute;
  top: -6rem;
  left: -0.86rem;

  .guide {
    margin-left: 2rem;
  }

  .guide-title {
    margin: 0 0 0.36rem -0.5rem;
  }

  .guide-second-title {
    margin: 0 0 0.36rem -0.5rem;
  }
}
</style>
