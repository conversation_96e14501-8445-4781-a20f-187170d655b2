<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { btnStatus } from '@/utils/constant'

@Component({})
export default class TaskArea extends Vue {
  @Prop({ type: Array, default: () => [] }) taskList;

  /**
   * 动态按钮文案
   */
  get buttonText() {
    return (type) => {
      const buttonConfig = {
        1: '去登录',
        2: '去报名',
        3: '去转发',
        4: '去观看',
      };
      return buttonConfig[type];
    };
  }

  /**
   * 动态按钮文案
   */
  get completeButtonText() {
    return (type) => {
      const buttonConfig = {
        1: '已完成',
        2: '已报名',
        3: '已完成',
        4: '已观看',
      };
      return buttonConfig[type];
    };
  }

  getButtonStyle(item) {
    const  opacity= item.completionStatus === 2 ? 0.6 : 1;
    const background = item.completionStatus === 2 ? "#b6b6b6" : btnStatus.unComplete
    return {opacity, background}
  }
}
</script>

<template>
  <div class="task-area position-center">
    <div
      v-for="(item, index) in taskList"
      :key="`${index}Task`"
      class="task-area__item"
    >
      <div>
<!--        <van-image width="1.12rem" height="1.12rem" :src="item.picture" />-->
        <div class="word-position">
          <p>{{ item.taskName }}</p>
          <p :style="{width:index === 3 ? '3.81rem' : '4.8rem'}">{{ item.remark }}</p>
          <p v-if="item.describe">{{item.describe}}</p>
        </div>
      </div>
      <div>
        <p
          @click="$emit('handleTask', item)"
          :style="getButtonStyle(item)"
        >
          {{
            item.completionStatus === 0
              ? buttonText(item.taskType)
              : item.completionStatus === 1
              ? completeButtonText(item.taskType)
              : '已完成'
          }}
        </p>
        <p v-if="index === 3 ">({{ item.taskNum }}/2)</p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.task-area {
  top: 14.79rem;
  width: 6.7rem;

  &__item {
    position: relative;
    box-sizing: border-box;
    padding: 0.3rem 0.19rem 0.2rem;
    width: 6.7rem;
    min-height: 1.7rem;
    background-image: linear-gradient(90deg, #def6fe 0%, #e6fecd 100%), linear-gradient(#ffffff, #ffffff);
    background-blend-mode: normal, normal;
    border-radius: 0.3rem;
    border: solid 1px #a8d0da;
    //&:nth-of-type(2) {
    //  > div {
    //    div:nth-of-type(1) {
    //      p:nth-of-type(2) {
    //        width: 3.81rem;
    //      }
    //    }
    //  }
    //}
    > div {
      &:nth-of-type(1) {
        display: flex;
        align-items: center;
        .word-position {
          //margin-left: 0.14rem;
          > p {
            &:first-child {
              font-size: 0.32rem;
              font-weight: normal;
              font-stretch: normal;
              line-height: 0.34rem;
              letter-spacing: 0;
              color: #12253a;
            }
            &:nth-child(2) {
              line-height: 0.34rem;
              margin-top: 0.15rem;
              font-size: 0.26rem;
              color: #2b5e9f;
              width: 4.73rem;
            }
            &:nth-child(3) {
              line-height: 0.34rem;
              font-size: 0.26rem;
              color: #2b5e9f;
              width: 4.9rem;
            }
          }
        }
      }
      &:nth-of-type(2) {
        position: absolute;
        top: 0.6rem;
        right: 0.19rem;
        > p {
          &:nth-of-type(1) {
            font-size: 0.24rem;
            font-weight: normal;
            line-height: 0.34rem;
            text-align: center;
            letter-spacing: 0;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 1.2rem;
            min-height: 0.5rem;
            border-radius: 0.25rem;
            padding: 0 0.1rem;
          }
          &:nth-of-type(2) {
            font-size: 0.2rem;
            font-weight: normal;
            line-height: 0.34rem;
            letter-spacing: 0;
            color: #fd6a0e;
            text-align: center;
            //margin-top: 0.1rem;
          }
        }
      }
    }
    &:not(:first-child) {
      margin-top: 0.15rem;
      > div {
        div:nth-of-type(1) {
          p:nth-of-type(2) {
            width: 4.8rem;
          }
        }
      }
    }

  }
}
</style>
