<template>
  <div
    class="single-coupon-container"
    :style="{
      backgroundImage: `url(${awardImage})`,
    }"
  >
    <div class="single-coupon-wrapper">
      <div class="single-coupon-left" :style="getImage(coupon.img_url)"></div>
      <div class="single-coupon-content">
        <div class="single-coupon-content--prize font_28">{{ coupon.award_name }}</div>
        <div class="single-coupon-content--tip font_24 text_align_left">{{ coupon.grant_time }}</div>
      </div>
      <div class="single-coupon-prize--state font_28 flex_column" @click="clickJump(coupon)">去查看</div>
    </div>
    <div class="single-coupon-footer font_24 flex_between_center ">
      <div class="left">
        领奖账号：{{ coupon.mobile }}
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { conversion, getImgSize } from "../../../../utils";
import { State } from 'vuex-class'
import { jumpTime } from "@/utils/debounce";

@Component({
  methods: { conversion },
  components: {},
})
export default class SingleCoupon extends Vue {
  @Prop({
    type: Object,
  })
  coupon

  @Prop({
    type: Object,
  })
  pageTab

  @Prop({
    type: String,
    default: '',
  })
  itemImg!: string

  @Prop({
    type: String,
    default: '',
  })
  awardImage!: string

  @State('configData') configData: any

  getImage(imgUrl: string) {
    return {
      backgroundImage: `url(${imgUrl})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      ...getImgSize(imgUrl),
    }
  }

  get tip() {
    let numbers
    if (this.coupon.extra_content.kqValue)
      numbers = this.coupon.extra_content.kqValue / 100
    else if (this.coupon.award_amount) numbers = this.coupon.award_amount / 100

    return `${numbers}元`
  }

  @jumpTime()
  clickJump(item) {
    this.$emit('clickJump',item)
  }
}
</script>
<style scoped lang="less">
.single-coupon {
  &-container {
    width: 6.22rem;
    //height: 286px;
    background: #ffffff;
    border-radius: 0.2rem;
    margin: 0 auto 0.2rem;
    padding: 0.36rem 0.3rem 0.24rem 0.34rem;
    overflow: hidden;
  }

  &-wrapper {
    height: 1.4rem;
    width: 6.84rem;
    display: flex;
    padding-bottom: 0.26rem;
    box-sizing: border-box;
  }

  &-footer {
    box-sizing: border-box;
    color: #222222;
    margin-top: 0.2rem;
    padding-top: 0.24rem;
    line-height: 0.34rem;
    border-top: 2px solid #eaeaea;

    &-left {
      margin-left: 0.55rem;
    }

    &-right {
      margin-right: 0.55rem;
    }
  }

  &-content {
    margin-top: 0.15rem;
    margin-left: 0.28rem;
    flex: 1;

    &--prize {
      color: #222222;
      font-weight: 500;
      line-height: 0.4rem;
      text-align: left;
      font-style: normal;
    }

    &--desc {
      margin-top: 0.1rem;
      font-weight: 400;
      color: #999999;
      line-height: 0.34rem;
    }

    &--tip {
      margin-top: 0.1rem;
      font-weight: 400;
      color: #999999;
      line-height: 0.34rem;
    }
  }

  &-prize {
    white-space: nowrap;
    margin-top: 0.11rem;
    font-size: 0.27rem;
    font-weight: 600;
    color: #3b3b3b;

    &--date {
      white-space: nowrap;
      font-size: 0.25rem;
      color: #3b3b3b;
      opacity: 0.6;
      margin-top: 0.1rem;
    }

    &--state {
      width: 1.4rem;
      height: 0.6rem;
      background: linear-gradient( 90deg, #FFBE75 0%, #FF890C 100%);
      color: #ffffff;
      font-weight: 500;
      line-height: 0.4rem;
      border-radius: 0.3rem;
      margin-right: 0.6rem;
      margin-top: 0.45rem;
    }
  }
}
</style>
