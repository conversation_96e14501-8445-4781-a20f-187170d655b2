<template>
  <Background v-bind="bgLayout">
    <InitMask v-if="!isReady"></InitMask>
    <back-button></back-button>
    <div v-if="couponList.length > 0" class="user-award-container">
      <SingleCoupon
        v-for="(item, index) in couponList"
        :key="index"
        :coupon="item"
        :pageTab="pageTab"
        :awardImage="pageTab.awardImage"
        :itemImg="pageTab.itemImg"
        @clickJump="clickJump"
      ></SingleCoupon>
    </div>
    <div class="empty-item flex_column" v-if="couponList.length === 0">
      <div class="empty-icon" :style="getImage(pageTab.empty)"></div>
      <div
        class="empty"
        v-html="'您还未获得奖励，快去参与活动，</br>超多丰厚奖励等您拿哦~'"
      ></div>
    </div>
    <modal
      :bg-obj="{ background: pageTab.enlargeCardImage }"
      ref="cardRef"
      class="card-modal"
      :show-close-button="false"
    >
      <div class="card-close" @click="cardRef.show = false"></div>
    </modal>
  </Background>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref } from 'vue-property-decorator'
import InitMask from '@/components/mask/InitMask.vue'
import Background from '@/components/background/Background.vue'
import { buriedPoint, getImgSize, imageLoadedHandle } from "@/utils";
import { apis } from '@/utils/service/apis'
import { State } from 'vuex-class'
import SingleCoupon from './SingleCoupon.vue'
import moment from 'moment'
import BackButton from '@/components/back/BackButton.vue'
import Modal from '@/components/modal/Modal.vue'

@Component({
  components: {
    Modal,
    BackButton,
    Background,
    InitMask,
    SingleCoupon,
  },
})
export default class MyAward extends Vue {
  // 页面基础配置

  @Prop({
    type: Object,
    default: () => {
      return {
        layoutType: 'onePageTop',
        bgImage: '',
        color: '',
        pageHeight: '',
        subBgList: [],
        activityId: '',
        awardBtnPos: {},
        isShowLuckyTimes: false,
        luckyTimesInfo: {},
      }
    },
  })
  pageTab: any

  // 页面背景和布局配置
  get bgLayout() {
    const { layoutType, bgImage, subBgList, bgColor } = this.pageTab
    return {
      layoutType,
      bgImage: [
        bgImage,
        ...subBgList.map(item => item.image).filter(item => !!item),
      ],
      bgColor,
      isHandleTransparentTitle: true,
    }
  }

  isReady = false

  @State('configData') configData: any

  @Ref('cardRef') cardRef: any

  protected async created() {
    //图片加载完成执行callback
    await imageLoadedHandle([this.pageTab.bgImage], () => {
      this.getData()
      this.isReady = true
    })
  }

  /**
   * {
   *       award_name: '500京果儿',
   *       grant_time: '2023-12-6 12:12',
   *       expiry_time: '2024-12-12  23:59:59',
   *       img_url:
   *         'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250401155058826099058120600507_w140_h140.png',
   *       prize_channel: 'INNER_PRIZE1',
   *       award_source: '邀请活动',
   *       mobile: '188****8888',
   *     },
   *     {
   *       award_name: '低碳卡片',
   *       grant_time: '2023-12-6 12:12',
   *       expiry_time: '2024-12-12  23:59:59',
   *       img_url:
   *         'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250411105416566099058136003238_w140_h140.png',
   *       prize_channel: 'card',
   *       award_source: '邀请活动',
   *       mobile: '188****8888',
   *     },
   *
   */
  couponList = []

  getImage(imgUrl: string) {
    return {
      backgroundImage: `url(${imgUrl})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      ...getImgSize(imgUrl),
    }
  }

  clickJump(item) {
    if (item.prize_type === 'VOUCHER') return (this.cardRef.show = true)
    window.location.href = this.pageTab.linkUrl
  }

  async getData() {
    const {
      appId,
      source,
      pubms_code,
      item_type,
      activityId,
      storageName,
    } = this.configData

    const [res, err] = await apis.getRecord({
      pubms_code,
      app_id: appId,
      source,
      item_type,
      activity_id: activityId,
      token: localStorage.getItem(storageName),
      limit: 100,
    })
    console.log(res, err)
    if (err || res.rtn_flag != '9999') {
      this.$toast(res?.rtn_msg || '获取我的奖品失败')
      return
    }
    res.rtn_data.rights_flow_list.forEach(val => {
      val.expiry_time = moment(val.expiry_time).format('YYYY.MM.DD') //HH:mm:ss
      val.grant_time = moment(val.grant_time).format('YYYY.MM.DD HH:mm:ss')
    })
    this.couponList = res.rtn_data?.rights_flow_list || []
  }
}
</script>

<style lang="less" scoped>
.user-award-container {
  margin-top: 0.64rem;
  height: 12rem;
  overflow-x: hidden;
  scrollbar-width: none; /* 对于Firefox */
}

.card-modal {
  .card-close {
    position: absolute;
    bottom: -1rem;
    left: 2.7rem;
    width: 0.5rem;
    height: 0.5rem;
    background: url('https://psm.bangdao-tech.com/interaction-putting/20316/img/20250407143430681099058127000638_w50_h50.png')
      no-repeat;
    background-size: 100% 100%;
  }
}

.empty-item {
  position: absolute;
  top: 3.41rem;
  //left: 2.09rem;
  width: 100%;
  .empty {
    margin-top: 0.48rem;
    justify-content: center;
    align-items: center;
    line-height: 0.4rem;
    font-size: 0.3rem;
    color: #666666;
    text-align: center;
  }
}
</style>
