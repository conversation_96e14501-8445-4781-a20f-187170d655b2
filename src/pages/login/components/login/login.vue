<template>
  <Background v-bind="bgLayout">
    <InitMask v-if="!isReady" :loadingColor="pageTab.bgColor"></InitMask>
    <div class="content">
      <!--输入框-->
      <div class="login-content">
        <div class="phone-content">
          <van-field
            v-model="mobile"
            type="number"
            placeholder="请输入手机号"
            maxlength="11"
          >
          </van-field>
        </div>
        <div class="auth-content">
          <van-field
            v-model="code"
            class="smsCode"
            type="number"
            center
            placeholder="请输入验证码"
            maxlength="6"
          >
            <template #button>
              <van-button
                size="small"
                type="primary"
                :disabled="isSendCodeDisabled"
                @click="handleCode"
                >{{ sendText }}
              </van-button>
            </template>
          </van-field>
        </div>
        <div class="imd-join flex_column" @click="handleLogin">登录</div>
      </div>
    </div>
    <!--协议弹出框-->
    <DialogAgreement :visible.sync="agreementDialog">
      <div class="iframeS">
        <backButton
          :isCover="false"
          :isClick="true"
          :back-icon="IMAGE.BACK"
          @click="informedConsent"
        ></backButton>
        <iframe
          width="100%"
          height="100%"
          style="border: none;"
          frameborder="no"
          :src="pageTab.linkUrl"
        >
        </iframe>
      </div>
      <van-button class="agreeBtn" @click="informedConsent(true)">
        我已阅读并知晓
      </van-button>
      <!--:disabled="noAgree"-->
    </DialogAgreement>
    <area-modal
      :page-tab="pageTab"
      ref="areaRef"
      @positionClick="positionClick"
    ></area-modal>
    <!-- 滑块 -->
    <Verify
      mode="pop"
      baseUrl="https://napi.bangdao-tech.com/gateway.do"
      @success="verifySuccess"
      captchaType="blockPuzzle"
      :imgSize="{ width: '280px', height: '160px' }"
      ref="verify"
    ></Verify>
  </Background>
</template>
<script lang="ts">
import { Component, Vue, Prop, Ref } from 'vue-property-decorator'
import { Verify } from '@bangdao/captcha-multi/packages/vue'
import { encrypt } from '@/utils/RSA'
import { buriedPoint } from '@/utils/common'
import { Checkbox } from 'vant'
import { apis } from '@/utils/service/apis'
import { imageLoadedHandle } from '@/utils'
import { State } from 'vuex-class'
import InitMask from '@/components/mask/InitMask.vue'
import Background from '@/components/background/Background.vue'
import BackButton from '@/components/IframeBack/index.vue'

import DialogAgreement from './Dialog.vue'
import { jumpTime } from '@/utils/debounce'
import { IMAGE } from '@/utils/constant'
import store from '@/store'
import { getLocation } from '@/utils/utilMap'
import AreaModal from '@/pages/login/components/login/AreaModal.vue'

Vue.use(Checkbox)

@Component({
  computed: {
    IMAGE() {
      return IMAGE
    },
  },
  components: {
    AreaModal,
    Verify,
    InitMask,
    Background,
    DialogAgreement,
    BackButton,
  },
})
export default class Login extends Vue {
  @Ref('verify') readonly verify!: Verify
  // 页面基础配置
  @Prop({ type: Object }) pageTab: any

  // 页面背景和布局配置
  get bgLayout() {
    const { layoutType, bgImage, bgColor } = this.pageTab
    return {
      layoutType,
      bgImage: bgImage,
      bgColor,
    }
  }

  isReady = false

  mobile = '' //手机号
  code = '' //验证码
  private countdown = 59
  private isSendCodeDisabled = false // 发送验证码是否置灰
  private sendText: any = '发送验证码' // 发送验证码是否置灰
  activeIcon =
    'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250115192916299107055066502255_w24_h25.png'
  inactiveIcon =
    'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250115192911760107055066402997_w24_h25.png'
  checked = false

  // 协议弹出框
  private agreementDialog = false

  @Ref('areaRef') areaRef: any

  protected async created() {
    //图片加载完成执行callback
    await imageLoadedHandle([this.pageTab.bgImage], () => {
      this.isReady = true
      this.countdown = 59
      this.checked = store.state.isChecked
    })
  }

  // mounted(){
  //   this.areaRef.open({
  //     areaTip: this.pageTab.areaTips.messageTip,
  //     areaType: 'posPart',
  //   })
  // }

  @State('configData') configData: any

  @jumpTime()
  informedConsent(isChecked?: boolean) {
    buriedPoint('点击 我已阅读并知晓 ')
    this.agreementDialog = false
    if (isChecked && !store.state.isChecked) {
      store.commit('SET_CHECKED', true)
      return
    }
    this.checked = store.state.isChecked
  }

  showDialogAgreement() {
    buriedPoint('点击隐私协议')
    this.agreementDialog = true
    store.commit('SET_CHECKED', this.checked)
  }

  @jumpTime()
  handleBack() {
    this.$router.go(-1)
  }

  @jumpTime()
  handleCode() {
    buriedPoint('点击获取验证码')
    if (!/^((\+|00)86)?1[3-9]\d{9}$/.test(this.mobile)) {
      this.$toast('请填写正确的手机号')
      return
    }
    this.verify.show()
  }

  /**
   * 定位方法
   *
   */
  verifyLocation() {
    this.areaRef.open({
      areaTip: this.pageTab.areaTips.messageTip,
      areaType: 'posPart',
    })
  }

  //高德定位
  positionClick(data) {
    if (data === 'posPart') {
      this.$toast.loading({
        message: '位置正在搜索中...',
        forbidClick: true,
        duration: 0,
      })
      getLocation(this.checkArea, this.errDialog)
    } else {
      this.areaRef.close()
    }
  }

  private async checkArea(citySearchResult: any) {
    const { activity_id, pubms_code, app_id: appId } = this.pageTab
    //调取接口 获取位置
    const [res, err] = await apis.location({
      relate_id: activity_id,
      pubms_code,
      location: `${citySearchResult.position.lng},${citySearchResult.position.lat}`,
      app_id: appId,
    })
    this.$toast.clear()
    if (err) {
      const { rtn_msg, rtn_flag } = err
      if (rtn_flag === '04011011') {
        this.areaRef.open({
          areaTip: this.pageTab.areaTips.isErrorTip,
          areaType: 'posWarn',
        })
        return
      }
      this.$toast(rtn_msg || '当前人数过多，请稍后在尝试～')
    }
    if (res) {
      // const {extend_map: {isLocation} } = res;
      // if(isLocation === 'T'){
      await this.sendSmsCode()
      // }
    }
  }

  private errDialog() {
    this.$toast.clear()
    this.areaRef.open({
      areaTip: this.pageTab.areaTips.isIosTip,
      areaType: 'posWarn',
      showBtn: false,
    })
  }

  //滑块验证成功
  async verifySuccess() {
    this.verify.refresh()
    if (this.pageTab.showArea) {
      return await this.sendSmsCode()
    }
    this.verifyLocation()
  }

  async sendSmsCode() {
    const { errorList } = this.configData
    const { source, pubms_code, activity_id: relate_id, app_id,item_type } = this.pageTab
    const [res, err] = await apis.sendCode({
      pubms_code,
      app_id,
      relate_id,
      busi_type: item_type,
      item_type: item_type,
      source,
      mobile: encrypt(this.mobile),
    })
    if (err) {
      const { rtn_flag, rtn_msg } = err
      const flag = errorList.find(item => item.code === rtn_flag)?.codeText
      return this.$toast(flag ?? rtn_msg ?? '验证码发送失败')
    }
    this.$toast('验证码发送成功')
    this.countDown()
  }

  verifyLogin() {
    if (!this.mobile) {
      this.$toast('请输入手机号')
      return
    }
    if (!/^((\+|00)86)?1[3-9]\d{9}$/.test(this.mobile)) {
      this.$toast('请填写正确的手机号')
      return
    }

    if (!this.code) {
      this.$toast('请填写验证码')
      return
    }
    // if (!this.checked) {
    //   this.$toast('请勾选用户隐私协议')
    //   return
    // }
    return true
  }

  //登录
  @jumpTime()
  async handleLogin() {
    buriedPoint('点击登陆')
    if (!this.verifyLogin()) return
    const { storageName, errorList } = this.configData
    const {
      source,
      pubms_code,
      activity_id: relate_id,
      app_id,
      item_type,
    } = this.pageTab
    const [res, err] = await apis.login({
      app_id,
      pubms_code,
      source,
      relate_id,
      act_id: relate_id,
      item_type: item_type,
      busi_type: item_type,
      is_rsa: 'T',
      sms_code: this.code,
      mobile: encrypt(this.mobile),
    })
    if (err) {
      const { rtn_flag, rtn_msg } = err
      const flag = errorList.find(item => item.code === rtn_flag)?.codeText
      return this.$toast(flag ?? rtn_msg ?? '系统繁忙，请稍候')
    }
    localStorage.setItem(storageName, res.user_token)
    buriedPoint('登录成功')
    await this.$router.replace('/')
  }

  @jumpTime()
  jumpToRule() {
    buriedPoint('点击规则按钮')
    this.$router.replace({
      path: '/rule',
      query: {
        url: this.configData.url,
      },
    })
  }

  /**
   * 倒计时
   * @private
   */
  private countDown() {
    this.isSendCodeDisabled = true
    let num = 59
    const times: any = setInterval(() => {
      this.sendText = num + 's'
      num--
      if (num < 0) {
        this.sendText = '获取验证码'
        this.isSendCodeDisabled = false
        clearInterval(times)
      }
    }, 1000)
  }
}
</script>

<style lang="less">
.login-content {
  .van-cell {
    background: #ffffff;

    width: 6.22rem;
    height: 1rem;
    color: #999999;
    padding: 0.32rem 0 0.32rem 0.36rem ;
    border-radius: 0.2rem;
    box-sizing: border-box;
  }

  .van-field__button {
    display: flex;
    min-width: 1.2rem;
  }

  .van-button--primary {
    width: 1.70rem;
    height: 1rem;
    background: #3DC427;
    border-radius: 0 0.2rem 0.2rem 0;
    font-size: 0.24rem;
    font-weight: 400;
    color: #FFFFFF;
    border: none;
  }

  .van-field__control::-webkit-input-placeholder {
    color: #999999;
  }

  .van-field__control {
    color: #999999;
  }
}
</style>

<style lang="less" scoped>
.content {
  position: relative;
  top: 8.75rem;
  width: 6.86rem;
  height: 4.92rem;
  background: rgba(255,255,255,0.7);
  border-radius: 0.24rem;
  border: 2px solid rgba(138,202,60,0.21);
  margin: 0 auto;
}

.login-content {
  padding: 0.4rem 0.32rem 0;
  .auth-content {
    margin-top: 0.44rem;
  }
}

.service {
  margin: 0.32rem 0 0.3rem 0rem;
  font-size: 0.24rem;
  color: #3c317e;

  .img-icon {
    width: 0.24rem;
    height: 0.25rem;
  }

  span {
    color: #1a96ff;
  }
}

.imd-join {
  margin-top: 0.4rem;
  width: 6.22rem;
  height: 1rem;
  background: #3DC427;
  border-radius: 0.2rem;

  font-weight: 400;
  font-size: 0.28rem;
  color: #FFFFFF;
}

.iframeS {
  flex-grow: 1;
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
}

.agreeBtn {
  background-image: linear-gradient(147deg, #ff9610 0%, #ff7528 100%);
  box-shadow: 0 8px 40px -8px rgba(194, 92, 24, 0.4);
  border-radius: 0.5rem;
  font-family: PingFangSC-Regular;
  font-size: 0.3rem;
  color: #ffffff;
  text-align: center;
  line-height: 0.9rem;
  width: 4rem;
  margin: 10px auto;
}
</style>
