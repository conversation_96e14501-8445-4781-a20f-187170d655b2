Object(页面配置) {
  pageTab(基础配置): Tab {
    bgImage(背景图片): Image [tip: '尺寸:750*1496+；背景过长时，可以将背景切分，非首屏展示区的背景图片配置在"延伸背景"中'],
    bgColor(背景颜色): Color,
    layoutType(页面布局): Enum {
      onePageTop(一屏展示【背景图片:750*1496】),
      imageSizeTop(图长可滚动【背景图片:750*任意】)
    },
    subBgList(延伸背景): Array [tip: 尺寸：750*任意，长图背景可切分非首屏的背景配置在此处，加快首页渲染, rely: $layoutType$ === 'imageSizeTop'] {
      image(背景图片): Image
    },
    activity_id(活动Id): String,
    app_id(appId): String,
    item_type(类型): String,
    pubms_code(机构码): String,
    showArea(是否显示区域): <PERSON><PERSON><PERSON>,
    source(渠道来源): String,
    linkUrl(下载链接): Link,
    loginBtn(登录按钮): Image,
    areaTips(区域提示): Object {
      messageTip(消息提示): String,
      isTip(是否提示): String,
      isErrorTip(错误提示): String,
      isIosTip(iOS提示): String
    }
  }
}
