<template>
  <transition name='dialog-transition'>
    <div v-if='show' class='page-mask' @touchmove.prevent>
      <div
        class='dialog-container'
        :style='{
          backgroundImage: `url(${dialogBg})`,
        }'
      >
        <div class="content font_36 flex_center" v-html="pageTab.noAwardContent"></div>
<!--        <div class='describe' :style='descirbeBg'></div>-->
        <div class="dialog-button flex_between_center" v-if="!complete">
          <div class="left-button" @click="$emit('backChallenge')"></div>
          <div class="right-button" @click="$emit('continueLevel')"></div>
        </div>
        <div class="dialog-close" @click="$emit('backChallenge')"></div>
      </div>
      <!--      <div class="dialog-close" @click="$router.replace({ path: '/' })"></div>-->
    </div>
  </transition>
</template>

<script lang='ts'>
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator'
import { buriedPoint } from '@/utils'

@Component
export default class LoginPop extends Vue {
  @Model('change', { type: Boolean, default: false }) readonly show!: boolean

  // 弹窗展示禁止背景滚动的兼容处理，记录当前滚动位置
  private top = 0

  // 固定视窗，禁止滚动
  @Watch('show')
  onShowChanged(val: boolean) {
    if (window._SE_ACT_TEMPLATE_ENV === 'editor') return
    //兼容处理，弹窗出现背景可滚动的场景
    const $mainPage = document.querySelector('#app') as HTMLElement
    if (val) {
      this.top = window.scrollY
      $mainPage.style.position = 'fixed'
      $mainPage.style.top = -this.top + 'px'
    } else {
      $mainPage.style.position = ''
      $mainPage.style.top = ''
      $mainPage.style.zIndex = '1'
      // 回到原先的top
      window.scrollTo(0, this.top)
    }
  }

  @Prop({ type: String, default: '' }) dialogBg: string | undefined
  @Prop({ type: String, default: '' }) content: string | undefined
  @Prop({}) showOther: any
  @Prop({}) pageTab: any
  @Prop({}) complete: any

  // get descirbeBg() {
  //   const img = 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20240828162035714082058050505000_w442_h160.png'
  //   const backgroundImage = `url(${img})`
  //   return { backgroundImage }
  // }
}
</script>
<style lang='less' scoped>
.dialog-transition-enter-active,
.dialog-transition-leave-active {
  transition: all 0.3s;
}

.dialog-transition-enter,
.dialog-transition-leave-to {
  opacity: 0;
  transform: translate3d(0, 0, 0);
}

.page-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1999;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  overflow: hidden;

  .dialog-container {
    position: absolute;
    left: 10%;
    transform: translateX(-50%);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 6.22rem;
    height: 10.66rem;
    box-sizing: border-box;
    display: flex;
    // justify-content: center;
    flex-direction: column;
    align-items: center;
    // 卡片弹出动画
    animation: dialog 0.35s ease-in forwards;
    transform-origin: center center;
    @keyframes dialog {
      0% {
        transform: scale(0);
        top: 7rem;
      }
      100% {
        transform: scale(1);
        top: 1.5rem;
      }
    }

    .content {
      position: absolute;
      top: 3.4rem;
      width: 100%;
      line-height: 0.48rem;
      color: #2b5e9f;
    }
    .dialog-button {
      position: absolute;
      top: 8.15rem;
      left: 0.65rem;
      width: 4.84rem;
      height: 0.8rem;
      .left-button , .right-button{
        width: 2.20rem;
        height: 0.8rem;
      }
    }

  }

  .dialog-close {
    width: 0.5rem;
    height: 0.5rem;
    position: absolute;
    top: 10.15rem;
    left: 2.87rem;

  }
}
</style>
