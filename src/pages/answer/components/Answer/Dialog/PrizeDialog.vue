<template>
  <transition name="dialog-transition">
    <div v-if="show" class="page-mask" @touchmove.prevent>
      <div
        class="dialog-container"
        :style="{
          backgroundImage: `url(${dialogBg})`,
        }"
      >
        <div class="card" v-if="prizeType == 'card'">
          <div class="card-icon" :style="getImage(prizeImg)" @click="$emit('showCard')"></div>
          <div class="card-title">{{ pageTab.hasJoinContent }}</div>
        </div>
        <div class="virtual" v-else>
          <div class="virtual-bg flex_column">
            <div class="virtual-icon" :style="getImage(pageTab.imgList.prizeVirtualImg)"></div>
            <div class="name font_46 flex_center">{{awardObj.award_name}}</div>
          </div>
          <div class="mobile font_32 ">领取手机号：{{mobile}}</div>
          <div class="tips font_20 text_align_center"> {{ pageTab.tips }}</div>
        </div>
        <div class="dialog-button flex_between_center"  v-if="!complete">
          <div class="left-button" @click="$emit('backChallenge')"></div>
          <div class="right-button" @click="$emit('continueLevel')"></div>
        </div>
        <div class="dialog-close" @click="$emit('backChallenge')"></div>
      </div>
    </div>
  </transition>
</template>

<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator'
import { buriedPoint, conversion, getImgSize } from '@/utils'

@Component({
  methods: { conversion },
})
export default class LoginPop extends Vue {
  @Model('change', { type: Boolean, default: false }) readonly show!: boolean

  // 弹窗展示禁止背景滚动的兼容处理，记录当前滚动位置
  private top = 0

  // 固定视窗，禁止滚动
  @Watch('show')
  onShowChanged(val: boolean) {
    if (window._SE_ACT_TEMPLATE_ENV === 'editor') return
    //兼容处理，弹窗出现背景可滚动的场景
    const $mainPage = document.querySelector('#app') as HTMLElement
    if (val) {
      this.top = window.scrollY
      $mainPage.style.position = 'fixed'
      $mainPage.style.top = -this.top + 'px'
    } else {
      $mainPage.style.position = ''
      $mainPage.style.top = ''
      $mainPage.style.zIndex = '1'
      // 回到原先的top
      window.scrollTo(0, this.top)
    }
  }

  @Prop({ type: String, default: '' }) dialogBg: string | undefined
  @Prop({ type: String, default: '' }) content: any
  @Prop({ type: String, default: '' }) prizeContent: any
  @Prop({ type: String, default: '' }) correctCount: string | undefined
  @Prop({ type: String, default: '' }) prizeImg: string | undefined
  @Prop({ type: String, default: '' }) prizeType: string | undefined
  @Prop({}) showOther: any
  @Prop({}) awardObj: any
  @Prop({}) pageTab: any
  @Prop({}) mobile: any
  @Prop({}) complete: any

  getImage(imgUrl: string) {
    return {
      backgroundImage: `url(${imgUrl})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      ...getImgSize(imgUrl),
    }
  }
}
</script>
<style lang="less" scoped>
.dialog-transition-enter-active,
.dialog-transition-leave-active {
  transition: all 0.3s;
}

.dialog-transition-enter,
.dialog-transition-leave-to {
  opacity: 0;
  transform: translate3d(0, 0, 0);
}

.page-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1999;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  overflow: auto;

  .dialog-container {
    position: absolute;
    left: 5%;
    transform: translateX(-50%);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 6.7rem;
    height: 10.66rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    // 卡片弹出动画
    animation: dialog 0.35s ease-in forwards;
    transform-origin: center center;
    @keyframes dialog {
      0% {
        transform: scale(0);
        top: 7rem;
      }
      100% {
        transform: scale(1);
        top: 1.5rem;
      }
    }

    .card {
      position: absolute;
      top: 3.41rem;
      left: 1.55rem;
      .card-title{
        margin-top: 0.32rem;
        font-size: 0.26rem;
        line-height: 0.34rem;
        color: #999999;
      }
    }

    .virtual {
      position: absolute;
      top: 3.44rem;
      left: 1.54rem;

      .virtual-bg{
        width: 3.86rem;
        height: 2.98rem;
        background-color: #ffffff;
        border-radius: 0.15rem;
        .name{
          font-weight: bold;
          line-height: 0.34rem;
          color: #FD8301;
        }
      }


      .mobile{
        margin: 0.2rem 0;
        font-weight: 600;
        color: #2C3D50;
        line-height: 0.44rem;
      }
      .tips{
        width: 3.44rem;
        font-weight: 500;
        color: #A8A8A8;
        line-height: 0.28rem;
        font-style: normal;
      }
    }

    .dialog-button {
      position: absolute;
      top: 8.15rem;
      left: 0.95rem;
      width: 4.84rem;
      height: 0.8rem;
      .left-button , .right-button{
       width: 2.20rem;
        height: 0.8rem;
      }
    }
  }

  .dialog-close {
    width: 0.5rem;
    height: 0.5rem;
    position: absolute;
    top: 10.15rem;
    left: 3.1rem;

  }
}
</style>
