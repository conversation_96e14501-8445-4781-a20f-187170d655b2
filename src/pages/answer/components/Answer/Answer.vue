<template>
  <Background v-bind="bgLayout">
    <InitMask v-if="!isReady" :loadingColor="pageTab.bgColor"></InitMask>
    <back-button @isGoBack="back" :isBack="true"></back-button>
    <div class="number font_36">
      开始答题 {{ currentQuestion }}/{{ totalQuestion }}
    </div>
    <div class="page-content-question">
      <!-- 问答标题 -->
      <div class="question-title">
        <div class="tag-item">
          <img :src="pageTab.imgList.titleIcon" alt="" class="tag" />
          <p class="title">{{ questionName }}</p>
        </div>
      </div>
      <!-- 选项 -->
      <div class="options-wrapper">
        <div
          v-for="(item, index) in optionsList"
          :key="item.index"
          class="options-item font_28"
          :class="{
            fail: item.index === select && item.index !== answer,
            success: select && item.index === answer,
          }"
          @click="changeSelect(item)"
        >
          <div>{{ index + 1 }}{{ item.value ? '、' : '' }}{{ item.value }}</div>
          <img
            :src="pageTab.imgList.successImage"
            alt=""
            v-if="select && item.index === answer"
          />
          <img
            :src="pageTab.imgList.failImage"
            alt=""
            v-else-if="item.index === select && item.index !== answer"
          />
        </div>
      </div>
      <!--答错后的弹出框提示-->
      <!-- <div class="result-error" v-if="resultError">
        <div class="correct-tip">
          <div class="tip font_34 ">正确答案：{{ answerContent }}</div>
        </div>
        <p class="analysis font_30">{{ analysis }}</p>
        <button
          class="next-btn"
          @click="nextQuestion"
          v-if="currentQuestion < totalQuestion"
        >
          下一题
        </button>
        <button class="next-btn" @click="submit" v-else>
          提交
        </button>
      </div> -->
    </div>
    <!-- 退出弹窗 -->
    <ExitDialog
      v-model="showExit"
      :dialogBg="pageTab.imgList.exitDialogBg"
      :content="pageTab.exitDialogContent"
      @stopTimer="stopTimer"
      @continueExit="continueExit"
    ></ExitDialog>
    <!-- 未中奖弹窗 -->
    <NoPrizeDialog
      v-model="showNoPrize"
      :pageTab="pageTab"
      :dialogBg="noPrizeDialogBg"
      :showOther="showOther"
      :content="noPrizeContent"
      :complete="isComplete"
      @continueLevel="continueLevel"
      @backChallenge="backChallenge"
    ></NoPrizeDialog>
    <!-- 中奖弹窗 -->
    <PrizeDialog
      v-model="showPrize"
      :pageTab="pageTab"
      :dialogBg="prizeDialogBg"
      :content="awardDesc"
      :mobile="mobile"
      :award-obj="awardObj"
      :prizeContent="prizeContent"
      :prizeImg="pageTab.imgList.prizeCardImg"
      :prizeType="prizeType"
      :showOther="showOther"
      :complete="isComplete"
      @showCard="showCard"
      @backChallenge="backChallenge"
      @continueLevel="continueLevel"
    ></PrizeDialog>
    <modal
      :bg-obj="{ background: pageTab.imgList.enlargeCardImage }"
      ref="cardRef"
      class="card-modal"
      :show-close-button="false"
    >
      <div
        class="card-close"
        @click="
          cardRef.show = false
          backChallenge()
        "
      ></div>
    </modal>
    <modal
      ref="modalLottieRef"
      class="lottie-modal"
      :show-close-button="false"
      :bg-obj="{ background: '' }"
    >
      <div class="lottie-container" id="lottieContainer"></div>
    </modal>
    <answer-other-modal
      ref="answerOtherRef"
      :page-tab="pageTab"
      @jumpOperation="continueLevel"
      @backOperation="backChallenge"
    ></answer-other-modal>
  </Background>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref } from 'vue-property-decorator'
import InitMask from '@/components/mask/InitMask.vue'
import { imageLoadedHandle, preLoadImage } from '@/utils'
import { apis } from '@/utils/service/apis'
import { buriedPoint } from '@/utils/common'
import ExitDialog from './Dialog/ExitDialog.vue'
import NoPrizeDialog from './Dialog/NoPrizeDialog.vue'
import PrizeDialog from './Dialog/PrizeDialog.vue'
import { showErrorMsg } from '@/utils/showErrorMsg'
import Background from '@/components/background/Background.vue'

import { jumpTime } from '@/utils/debounce'
import BackButton from '@/components/back/BackButton.vue'
import Modal from '@/components/modal/Modal.vue'
import AnswerOtherModal from '@/components/answerChallengeModal/answerOtherModal.vue'
import { State } from 'vuex-class'
import lottie from 'lottie-web'

@Component({
  computed: {},
  components: {
    AnswerOtherModal,
    Modal,
    BackButton,
    InitMask,
    ExitDialog,
    NoPrizeDialog,
    PrizeDialog,
    Background,
  },
})
export default class Answer extends Vue {
  // 页面基础配置
  @Prop({
    type: Object,
  })
  pageTab

  isReady = false
  showAnswer = false // 答案弹窗
  showExit = false // 退出弹窗
  showNoPrize = false // 未中奖弹窗
  showPrize = false // 中奖弹窗
  selectList: any = [] // 选择列表
  currentQuestion = 1 // 当前题号
  questions: any = [] // 问题列表
  select = '' // 选择
  flow_id = ''
  award = ''

  submitClick = false //是否提交按钮
  noPrizeDialogBg = '' // 没有中奖弹窗背景
  noPrizeContent = '' // 没有中奖弹窗文案
  prizeDialogBg = '' //中奖弹框

  done = false
  exitConfirmVisible = false
  exitConfirmResult: any = null

  //中奖描述
  awardDesc = ''
  awardObj = {}
  //
  prizeContent = ''
  mobile = ''

  /**
   * 答题后温馨提示
   *
   */
  resultError = false

  prizeType = 'card' //是否是卡片

  showOther = false //是否发奖失败

  qNum = 0 // 次数

  isComplete = false //是否通关

  private animation: any // 动效

  isPassChance = true //是否通过次数

  // 添加计时器属性
  private nextQuestionTimer: any = null;

  @Ref('cardRef') cardRef: any
  @Ref('answerOtherRef') answerOtherRef: any
  @State('configData') configData: any

  @Ref('modalLottieRef') modalLottieRef: any

  // 页面背景和布局配置
  get bgLayout() {
    const { layoutType, bgImage, subBgList, bgColor } = this.pageTab
    return {
      layoutType,
      bgImage: [
        bgImage,
        ...subBgList.map(item => item.image).filter(item => !!item),
      ],
      bgColor,
      isHandleTransparentTitle: true,
    }
  }

  get questionName() {
    return this.questions[this.currentQuestion - 1]?.name
  }

  // 获取选项列表
  get optionsList() {
    return this.questions[this.currentQuestion - 1]?.options || []
  }

  // 获取题目类型
  get questionType() {
    return this.questions[this.currentQuestion - 1]?.type || ''
  }

  // 获取总题数
  get totalQuestion() {
    return this.questions.length || 0
  }

  // 当前题选择的选项
  get options() {
    return this.optionsList[this.currentQuestion - 1]
  }

  // 答案
  get answer() {
    return this.optionsList.find(item => item.is_answer)?.index || ''
  }

  get answerContent() {
    return this.optionsList.find(item => item.is_answer)?.value || ''
  }

  // 解析
  get analysis() {
    return this.questions[this.currentQuestion - 1]?.analysis || ''
  }

  getPrev(index) {
    const option = this.questions[index]?.options
    return option.find(item => item.is_answer)?.index || ''
  }

  protected async created() {
    //图片加载完成执行callback
    await imageLoadedHandle([this.pageTab.bgImage], async () => {
      await this.getQuestions()
      this.isReady = true
    })
    preLoadImage(Object.values(this.pageTab.imgList))
  }

  showComplete(){
    this.modalLottieRef.show = true;
    setTimeout(() => {
      this.initLottie(this.configData.lottieJson, 'lottieContainer', false)
    })
  }

  // 动画设置
  private initLottie(data: any, eleId: string, loop = true) {
    const _id = document.getElementById(eleId)
    if (_id) {
      this.animation = lottie.loadAnimation({
        container: _id, // 当前需要渲染的DOM
        renderer: 'svg',
        loop, // 是否循环播放
        autoplay: true, // 是否自动播放
        path: data,
      })
      this.animation.addEventListener('complete', () => {
        this.modalLottieRef.show = false;
        this.isPassChance ? ( this.showPrize = true) : (this.showNoPrize = true)
      })
    }
  }

  mounted() {
    // this.awardDesc = '5元优惠卷'
    // this.awardObj = {award_desc :"满80元立减 | 自领取之日5天内有效"}
    // this.noPrizeDialogBg = this.pageTab.imgList.noPrizeDialogBg
    // this.noPrizeContent =  this.wrongContinue ? this.pageTab.noPrizeContent.worry : this.pageTab.noPrizeContent.time
    // this.showNoPrize = true
    // this.prizeDialogBg = this.pageTab.imgList.prizeDialogBg
    // this.prizeContent = this.wrongContinue ? this.pageTab.noPrizeContent.worry : this.pageTab.noPrizeContent.time
    // this.showPrize = true
    // this.showTime = true
  }

  // 获取题目列表
  async getQuestions() {
    const token = localStorage.getItem(this.configData.storageName) || ''
    const [res, err] = await apis.getQuestion({
      pubms_code: this.pageTab.pubms_code,
      app_id: this.pageTab.app_id,
      source: this.pageTab.source,
      activity_id: this.pageTab.activity_id,
      item_type: this.pageTab.item_type,
      token,
    })
    console.log(res, err)
    if (err) {
      this.done = true
      this.$toast(err?.rtn_msg || res?.rtn_msg || '获取题目失败')
      return
    }
    this.questions = res?.rtn_data || []
  }

  // 选择选项
  changeSelect(item) {
    if (this.selectList[this.currentQuestion - 1]) {
      return
    }
    this.select = item.index
    if (this.selectList.length >= this.currentQuestion) {
      this.selectList.splice(this.currentQuestion - 1, 1, {
        questionId: item.questionId,
        answer: item.index,
      })
    } else {
      this.selectList.push({
        questionId: this.questions[this.currentQuestion - 1]?.question_id,
        answer: item.index,
      })
    }

    // 清除之前的计时器（如果有）
    if (this.nextQuestionTimer) {
      clearTimeout(this.nextQuestionTimer);
    }

    // 设置2秒后自动进入下一题
    this.nextQuestionTimer = setTimeout(() => {
      if (this.currentQuestion < this.totalQuestion) {
        this.nextQuestion();
      } else {
        this.submit();
      }
    }, 2000);
  }

  // @jumpTime()
  // prev() {
  //   this.currentQuestion--
  //   const res: { [key: string]: any } = this.selectList[
  //     this.currentQuestion - 1
  //   ]
  //   this.select = res?.answer
  //   console.log('=======', res)
  // }

  // 下一题
  @jumpTime()
  nextQuestion() {
    if (!this.select) {
      this.$toast('请选择答案')
      return
    }

    // 清除计时器
    if (this.nextQuestionTimer) {
      clearTimeout(this.nextQuestionTimer);
      this.nextQuestionTimer = null;
    }

    this.resultError = false
    this.currentQuestion++
    this.select = ''
    this.showAnswer = false
  }

  // 提交
  @jumpTime()
  async submit() {
    buriedPoint('答题页提交按钮')
    if (this.submitClick) {
      return
    }
    this.submitClick = true
    const token = localStorage.getItem(this.configData.storageName) || ''
    const { source, pubms_code, app_id, activity_id, item_type } = this.pageTab
    this.$toast.clear()
    this.$toast.loading({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    })
    if (this.selectList.length < this.questions.length) {
      const selectQuestionIds = this.selectList.map(item => item.questionId)
      this.questions.forEach(question => {
        if (
          selectQuestionIds.findIndex(item => item === question.question_id) ===
          -1
        ) {
          this.selectList.push({
            questionId: question.question_id,
            answer: '',
          })
        }
      })
    }
    const [res, err] = await apis.addAnswer({
      source,
      pubms_code,
      app_id,
      activity_id,
      item_type,
      token,
      extend_params: JSON.stringify(this.selectList),
    })
    this.$toast.clear()
    this.done = true
    if (err) {
      const { rtn_msg, rtn_flag } = err
      const flag = this.pageTab.errorList.find(item => {
        return item.code == rtn_flag
      })
      showErrorMsg({
        showType: 'alert',
        message: flag?.codeText || rtn_msg || '问卷提交失败',
        oKBtnHandle: () => {
          this.stopBack()
        },
      })
      return
    }
    if (res) {
      const {
        rtn_data: { correctCount, grantResults, isUp, isComplete,qaNum },
      } = res
      this.mobile = res?.rtn_data?.mobile ?? ''
      this.qNum = qaNum
      this.isComplete = isComplete
      this.initAward(correctCount, grantResults, isUp, isComplete)
    }
  }


  initAward(correctCount: any, grantResults: any, isUp: any, isComplete: any) {
    if (!isUp) {
      buriedPoint('未中奖弹窗')
      return this.answerOtherRef.open({
        title: '闯关失败',
        content: this.pageTab.noPrizeContent,
        leftText: '返回首页',
        rightText: '继续闯关',
      })
    }
    if (grantResults && grantResults.length > 0) {
      const prize = grantResults[0]?.draw_prize_infos
      if (!prize && grantResults[0]?.sub_code) {
        this.noPrizeDialogBg = this.isComplete ? this.pageTab.imgList.noCompleteDialogBg : this.pageTab.imgList.noPrizeDialogBg
        this.noPrizeContent = this.pageTab.noAwardContent
        this.isPassChance = false
        if(this.isComplete){
          this.showComplete();
          return
        }
        this.showNoPrize = true
        buriedPoint('领奖失败')
        return
      }
      const isAward = grantResults[0]?.draw_prize_infos[0]
      this.awardDesc = isAward?.award_name
      this.prizeType = isAward?.prize_type == 'VOUCHER' ? 'card' : ''
      this.awardObj = isAward
      this.prizeDialogBg = this.isComplete ? this.pageTab.imgList.prizeCompleteDialogBg : this.pageTab.imgList.prizeDialogBg
      this.isPassChance = true
      if(this.isComplete){
        this.showComplete();
        return
      }
      this.showPrize = true
      buriedPoint('中奖弹窗')
    } else {
      this.answerOtherRef.open({
        title: '闯关失败',
        content: this.pageTab.noPrizeContent,
        leftText: '返回首页',
        rightText: '继续闯关',
      })
      buriedPoint('未中奖弹窗')
    }
  }



  back() {
    this.$router.replace({ path: '/' })
  }

  public exitConfirm() {
    console.log('====🚀====this.done=====🚀=====', this.done)
    if (this.done) {
      return Promise.resolve(true)
    }
    this.exitConfirmResult = null
    this.exitConfirmVisible = true
    this.showExit = true

    return new Promise(resolve => {
      const unwatch = this.$watch(
        () => this.exitConfirmResult,
        value => {
          // 停止监听
          unwatch()

          // 清空状态
          this.exitConfirmResult = null
          this.exitConfirmVisible = false
          this.showExit = false

          // 返回结果
          resolve(value)
        }
      )
    })
  }

  stopBack() {
    this.done = true
    this.back()
  }

  // 停止计时，并返回首页
  stopTimer() {
    buriedPoint('确认退出')
    this.done = true
    this.exitConfirmResult = true
  }

  continueExit() {
    buriedPoint('继续挑战')
    this.showExit = false
    this.exitConfirmResult = false
  }

  continueLevel() {
    buriedPoint('继续挑战')

    this.done = false
    this.showNoPrize = false
    this.showPrize = false

    if ((this.qNum <= 0 && !this.isComplete) || this.isComplete) {
      this.$toast(this.isComplete ? '您已通关，无法继续答题' : '次数不足，快去做任务获得闯关次数吧。')
      setTimeout(() => {
        this.backChallenge();
      },3000)
      return
    }
    // 重置题目和选项状态
    this.currentQuestion = 1
    this.select = ''
    this.selectList = []
    this.resultError = false
    this.showAnswer = false
    this.submitClick = false
    // 清除计时器
    if (this.nextQuestionTimer) {
      clearTimeout(this.nextQuestionTimer);
      this.nextQuestionTimer = null;
    }


    // 重新获取题目
    this.getQuestions()
  }

  @jumpTime()
  handleLottie() {
    buriedPoint('通关弹框_点击查看奖励')
    this.done = true
    this.modalLottieRef.show = false
    this.$router.replace({ path: '/myAward' })
  }

  backChallenge() {
    buriedPoint('返回首页')
    this.done = true
    this.showNoPrize = false
    this.showPrize = false
    this.$router.replace({ path: '/' })
  }

  showCard() {
    buriedPoint('查看卡片')
    this.done = true
    this.showPrize = false
    this.cardRef.show = true
  }

  clickToLink() {
    window.location.replace(this.pageTab.linkUrl)
  }

  // 在组件销毁时清除计时器
  beforeDestroy() {
    if (this.nextQuestionTimer) {
      clearTimeout(this.nextQuestionTimer);
      this.nextQuestionTimer = null;
    }
  }
}
</script>

<style lang="less" scoped>
.number {
  position: absolute;
  top: 5.56rem;
  left: 0.83rem;
  line-height: 0.34rem;
  color: #ffffff;
}

.page-content-question {
  position: absolute;
  top: 6.76rem;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

.question-title {
  width: 100vw;
  padding: 0 1.01rem 0 1rem;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .tag-item {
    overflow: hidden; /* 清除浮动影响 */
    //display: flex;
    //align-items: flex-start;

    .tag {
      float: left; /* 图片浮动到左侧 */
      margin-right: 0.2rem; /* 图片与文字之间的间距 */
      width: 0.9rem;
      height: 0.5rem;
    }

    .title {
      font-size: 0.32rem;
      font-weight: normal;
      font-stretch: normal;
      color: #12253a;
      line-height: 0.34rem;
      word-break: break-word;
    }
  }
}

.options-wrapper {
  margin-top: 0.5rem;
  width: 100vw;
  padding: 0 1rem 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 5rem;

  .options-item {
    width: 5.5rem;
    height: 0.9rem;
    background-image: linear-gradient(90deg, #def6fe 0%, #e6fecd 100%),
      linear-gradient(#ffffff, #ffffff);
    background-blend-mode: normal, normal;
    border-radius: 0.45rem;
    border: solid 1px #a8d0da;
    font-weight: normal;
    line-height: 0.34rem;
    color: #2b5e9f;
    margin-bottom: 0.3rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.13rem 0.5rem 0.14rem 0.36rem;
    box-sizing: border-box;

    img {
      width: 0.56rem;
      height: 0.56rem;
      position: relative;
      left: 0.4rem;
    }
  }

  .success {
    background-image: linear-gradient(90deg, #45d688 0%, #18b853 100%),
      linear-gradient(#45d688, #45d688);
    color: #ffffff;
  }

  .fail {
    background-image: linear-gradient(90deg, #fe8c64 0%, #fe6564 100%),
      linear-gradient(#fe6564, #fe6564);
    color: #ffffff;
  }

  @keyframes movePos {
    0% {
      transform: scale(1);
    }
    25% {
      transform: scale(1.1);
    }
    50% {
      transform: scale(0.9);
    }
    75% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }
}

.next {
  background-image: url('https://psm.bangdao-tech.com/interaction-putting/20316/img/20240730171117869100096075904337_w448_h108.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 4.48rem;
  height: 1.08rem;
  position: absolute;
  right: 1.55rem;
  bottom: -1.46rem;
}

.prev {
  background-image: url('https://psm.bangdao-tech.com/interaction-putting/20316/img/20240725173442746100096049603557_w265_h78.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 2.65rem;
  height: 0.78rem;
  position: absolute;
  left: 0.59rem;
  bottom: -1.26rem;
}

.next-commit {
  background-image: url('https://psm.bangdao-tech.com/interaction-putting/20316/img/20240725173438031100096049404209_w448_h108.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 4.48rem;
  height: 1.08rem;
  position: absolute;
  right: 1.55rem;
  bottom: -1.46rem;
}

.result-error {
  //height: 451px;
  //background-image: linear-gradient(0deg, #ffffff 0%, #ffffff 100%), linear-gradient(90deg, #e8f8ff 0%, #f2f3d2 100%), linear-gradient(#ffffff, #ffffff);
  background-image: linear-gradient(to bottom, #d3f0ff, #f7fbd9);
  background-blend-mode: normal, normal, normal;
  box-shadow: 0 3px 0 0 rgba(0, 0, 0, 0.05);
  border-radius: 0.4rem 0.4rem 0 0;

  min-height: 4.51rem;
  position: fixed;
  bottom: 0;
  width: 100%;
  padding-left: 0.39rem;
  padding-right: 0.56rem;
  box-sizing: border-box;

  .correct-tip {
    display: flex;
    margin: 0.6rem 0 0.5rem 0;

    .tip {
      color: #12253a;
      line-height: 0.34rem;
      font-size: 0.32rem;
    }
  }

  .analysis {
    color: #2b5e9f;

    line-height: 0.4rem;
    height: 1.2rem;
    max-height: 1.5rem;
    overflow-y: auto;
  }

  .next-btn {
    background-image: linear-gradient(90deg, #fece5a 0%, #fd6a0e 100%),
      linear-gradient(#fd6a0e, #fd6a0e);
    background-blend-mode: normal, normal;
    border-radius: 0.4rem;
    border: solid 1px #fd6e12;
    width: 2.2rem;
    height: 0.8rem;
    font-size: 0.32rem;
    color: #ffffff;
    margin: 0.59rem 0 0 4.4rem;
  }
}

.card-modal {
  .card-close {
    position: absolute;
    bottom: -1rem;
    left: 2.7rem;
    width: 0.5rem;
    height: 0.5rem;
    background: url('https://psm.bangdao-tech.com/interaction-putting/20316/img/20250407143430681099058127000638_w50_h50.png')
      no-repeat;
    background-size: 100% 100%;
  }
}

.lottie-modal {
  .lottie-container {
    width: 7.5rem;
    //height: 4rem;
  }

  .lottie-btn {
    position: absolute;
    top: 11rem;
    left: 2.3rem;
    background: url('https://psm.bangdao-tech.com/interaction-putting/20316/img/20250407151046647099058127003640_w312_h83.png')
      no-repeat;
    background-size: 100% 100%;
    width: 3.12rem;
    height: 0.83rem;
  }
}
</style>
