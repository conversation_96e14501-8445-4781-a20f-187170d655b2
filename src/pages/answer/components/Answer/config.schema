Object(页面配置) {
  pageTab(基础配置): Tab {
    bgImage(背景图片): Image [tip: '尺寸:750*1496+；背景过长时，可以将背景切分，非首屏展示区的背景图片配置在"延伸背景"中'],
    bgColor(背景颜色): Color,
    layoutType(页面布局): Enum {
      onePageTop(一屏展示【背景图片:750*1496】),
      imageSizeTop(图长可滚动【背景图片:750*任意】)
    },
    subBgList(延伸背景): Array [tip: 尺寸：750*任意，长图背景可切分非首屏的背景配置在此处，加快首页渲染, rely: $layoutType$ === 'imageSizeTop'] {
      image(背景图片): Image
    },
    activity_id(活动Id): String,
    app_id(appId): String,
    source(渠道来源): String,
    item_type(类型): String,
    pubms_code(机构码): String,
    linkUrl(下载链接): Link,
    imgList(图片配置): Object {
      titleIcon(标题图标): Image,
      successImage(成功图片): Image,
      failImage(失败图片): Image,
      exitDialogBg(退出弹窗背景): Image,
      prizeDialogBg(奖品弹窗背景): Image,
      prizeCardImg(奖品卡片图片): Image,
      prizeVirtualImg(虚拟奖品图片): Image,
      noPrizeDialogBg(未中奖弹窗背景): Image,
      closeImage(关闭按钮): Image,
      enlargeCardImage(放大卡片图片): Image,
      dialogBg(弹窗背景): Image,
      dialogOtherBg(其他弹窗背景): Image
    },
    errorList(错误文案配置): Array {
      codeText(错误描述): String,
      code(错误码): String
    },
    exitDialogContent(退出弹窗内容): String [tip: '支持<br/>换行'],
    noPrizeContent(未中奖内容): String [tip: '支持<br/>换行'],
    hasJoinContent(已参与内容): String [tip: '支持<br/>换行'],
    prizeContent(奖品内容): String,
    tips(提示文案): String,
    noAwardContent(奖品已发完提示): String [tip: '支持<br/>换行']
  }
}
