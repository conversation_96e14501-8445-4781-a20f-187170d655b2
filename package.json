{"name": "beijing-low-carbon-energy-wechat-act", "author": "68752 <<EMAIL>>", "version": "0.0.17", "private": true, "scripts": {"serve": "cli-act-template collection -c serve", "collection": "cli-act-template collection"}, "dependencies": {"@bangdao/captcha-multi": "^0.2.9", "jsencrypt": "^3.2.1", "moment": "^2.29.4", "lottie-web": "^5.12.2", "core-js": "^3.6.5", "vant": "^2.12.31", "vue": "^2.6.11", "vue-class-component": "^7.2.3", "vue-property-decorator": "^8.4.2", "vue-router": "^3.2.0", "vuex": "^3.6.2", "vuex-class": "^0.3.2"}, "devDependencies": {"@sitease/cli-act-template": "~2.2.2", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.4.0", "@vue/cli-plugin-typescript": "4.5.15", "@vue/cli-service": "~4.4.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "lint-staged": "^10.5.3", "prettier": "^1.19.1", "ts-import-plugin": "^1.6.7", "typescript": "4.4.2", "vconsole-webpack-plugin": "^1.5.2", "vue-template-compiler": "^2.6.11"}, "babel": {"presets": ["@vue/cli-plugin-babel/preset"]}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "prettier": {"useTabs": false, "tabWidth": 2, "semi": false, "singleQuote": true, "trailingComma": "es5"}, "lint-staged": {"*.{js,vue}": ["prettier --write", "vue-cli-service lint", "git add"]}}