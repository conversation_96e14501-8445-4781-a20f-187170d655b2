# 技术栈基本信息

## 项目概述

**项目名称**: beijing-low-carbon-energy-wechat-act  
**项目版本**: 0.0.17  
**项目描述**: 北京电力低碳用能微信活动项目  
**项目类型**: 微信H5活动页面  
**开发团队**: 邦道科技

## 核心技术栈

### 前端框架
- **Vue.js**: 2.6.11 - 主要前端框架
- **Vue Router**: 3.2.0 - 路由管理
- **Vuex**: 3.6.2 - 状态管理
- **TypeScript**: 4.4.2 - 类型安全开发

### UI组件库
- **Vant**: 2.12.31 - 移动端UI组件库
- **Lottie-web**: 5.12.2 - 动画效果库

### 开发工具链
- **Vue CLI**: 4.4.0 - 项目脚手架
- **Webpack**: 模块打包工具
- **Less**: 3.0.4 - CSS预处理器
- **ESLint**: 6.7.2 - 代码规范检查
- **Prettier**: 1.19.1 - 代码格式化

### 第三方依赖
- **Axios**: HTTP请求库（内置于request.ts）
- **Moment.js**: 2.29.4 - 时间处理
- **JSEncrypt**: 3.2.1 - RSA加密
- **@bangdao/captcha-multi**: 0.2.9 - 验证码组件
- **Core-js**: 3.6.5 - JavaScript标准库

### 开发依赖
- **@sitease/cli-act-template**: 2.2.2 - 邦道活动模板CLI
- **TypeScript相关**: 
  - @typescript-eslint/eslint-plugin: 2.33.0
  - @typescript-eslint/parser: 2.33.0
- **Vue相关插件**:
  - vue-class-component: 7.2.3
  - vue-property-decorator: 8.4.2
  - vuex-class: 0.3.2
- **构建工具**:
  - ts-import-plugin: 1.6.7
  - vconsole-webpack-plugin: 1.5.2

## 项目结构

```
beijing-low-carbon-energy-wechat/
├── public/                     # 静态资源
│   ├── config.json            # 项目配置文件
│   ├── index.html             # HTML模板
│   ├── sdk-default/           # 默认SDK
│   └── sdk-funeng/            # 赋能平台SDK
├── src/                       # 源代码
│   ├── apis/                  # API接口定义
│   │   ├── activity.ts        # 活动相关API
│   │   ├── putting.ts         # 投放相关API
│   │   ├── task.ts           # 任务相关API
│   │   └── common.d.ts       # 通用类型定义
│   ├── assets/               # 静态资源
│   │   └── styles/           # 样式文件
│   │       ├── base.less     # 基础样式
│   │       └── loading.less  # 加载样式
│   ├── components/           # 全局组件
│   │   ├── audioComponent/   # 音频组件
│   │   ├── background/       # 背景组件
│   │   ├── mask/            # 遮罩组件
│   │   ├── modal/           # 弹窗组件
│   │   ├── PuttingList/     # 投放列表组件
│   │   └── TaskList/        # 任务列表组件
│   ├── pages/               # 页面组件
│   │   ├── index/           # 首页
│   │   ├── answer/          # 答题页
│   │   ├── videoTask/       # 视频任务页
│   │   ├── login/           # 登录页
│   │   ├── myAward/         # 我的奖品页
│   │   └── rule/            # 规则页
│   ├── router/              # 路由配置
│   │   └── index.ts         # 路由定义
│   ├── store/               # Vuex状态管理
│   │   ├── modules/         # 状态模块
│   │   │   ├── award.ts     # 奖品状态
│   │   │   ├── music.ts     # 音乐状态
│   │   │   └── picture.ts   # 图片状态
│   │   ├── getters.ts       # 状态获取器
│   │   └── index.ts         # 状态入口
│   ├── utils/               # 工具函数
│   │   ├── service/         # 网络请求服务
│   │   │   ├── request.ts   # 基础请求封装
│   │   │   ├── requestService.ts # 请求服务基类
│   │   │   ├── apis.ts      # 业务API封装
│   │   │   └── lib/         # 加密解密库
│   │   ├── common.ts        # 通用工具函数
│   │   ├── constant.ts      # 常量定义
│   │   ├── showErrorMsg.ts  # 错误提示
│   │   └── index.ts         # 工具函数入口
│   ├── App.vue              # 根组件
│   ├── main.ts              # 入口文件
│   └── shims-my.d.ts        # 类型声明
├── doc/                     # 文档目录
├── .env                     # 环境变量
├── .eslintrc.js            # ESLint配置
├── .siteaserc.js           # 邦道SE配置
├── package.json            # 项目依赖
├── tsconfig.json           # TypeScript配置
├── vue.config.js           # Vue CLI配置
└── README.md               # 项目说明
```

## 环境配置

### 开发环境要求
- **Node.js**: 建议使用LTS版本（14.x+）
- **包管理器**: Yarn (推荐) 或 npm
- **网络要求**: 需要连接邦道VPN
- **浏览器**: 主要针对微信浏览器环境

### 构建配置
- **开发服务器**: webpack-dev-server
- **端口**: 8080
- **代理配置**: 支持mock数据代理
- **外部资源**: CDN资源引入（Vue、Vant等）
- **别名配置**: `@` 指向 `src` 目录

### SDK集成
项目集成了邦道SE SDK，支持两种环境：
- **funeng**: 赋能平台环境 (`./sdk-funeng/index.js`)
- **default**: 默认环境 (`./sdk-default/index.js`)

## 特殊配置

### 微信环境适配
- 支持微信JS-SDK (jweixin-1.6.0)
- 微信分享功能
- 微信授权登录

### 移动端适配
- 响应式设计（rem单位）
- 刘海屏适配
- iOS/Android兼容性
- vConsole移动端调试

### 安全特性
- RSA加密传输
- Base64编码
- Token认证机制
- 请求参数加密

### 代码规范
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **TypeScript**: 类型检查
- **Git Hooks**: 提交前代码检查

## 开发命令

```bash
# 安装依赖（需要连接邦道VPN）
yarn install

# 本地开发服务器
yarn serve

# 编辑预览和打包操作
yarn collection
```

## 浏览器兼容性

```json
{
  "browserslist": [
    "> 1%",
    "last 2 versions", 
    "not dead"
  ]
}
```

## 注意事项

1. **网络环境**: 开发时需要连接邦道VPN
2. **SDK切换**: 通过修改`.siteaserc.js`中的`sdkPath`字段切换SDK环境
3. **外部依赖**: 部分资源通过CDN引入，注意网络连接
4. **兼容性**: 主要针对微信浏览器环境优化
5. **加密**: 生产环境默认开启接口加密
6. **调试**: 可通过环境变量开启vConsole移动端调试
7. **构建**: 支持UMD模块打包，可集成到其他项目

## 技术特色

1. **模块化架构**: 清晰的分层架构，便于维护
2. **类型安全**: 全面使用TypeScript，提高代码质量
3. **组件化开发**: 可复用的组件库，提高开发效率
4. **移动端优化**: 专门针对移动端H5优化
5. **SDK集成**: 深度集成邦道SE SDK，支持多环境部署
