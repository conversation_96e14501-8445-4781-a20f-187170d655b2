# 接口模拟功能使用说明

## 概述

本项目提供了一套完整的接口模拟功能，基于Service Worker技术实现，可以在开发和测试阶段模拟后端接口响应，提高开发效率和测试覆盖率。

## 功能特性

### 🚀 核心功能
- **Service Worker拦截**: 基于Service Worker技术拦截网络请求
- **动态配置**: 支持运行时动态开启/关闭模拟功能
- **选择性模拟**: 可以选择性地模拟特定接口
- **延迟模拟**: 支持配置响应延迟，模拟真实网络环境
- **数据管理**: 支持自定义模拟数据，支持JSON格式
- **调试面板**: 提供可视化调试面板，方便开发调试

### 🛠️ 技术特性
- **零侵入**: 不需要修改现有业务代码
- **类型安全**: 完整的TypeScript类型定义
- **持久化**: 配置和数据自动保存到本地存储
- **热更新**: 支持运行时更新配置和数据
- **日志记录**: 详细的请求拦截日志

## 文件结构

```
project/
├── public/
│   ├── mock-service-worker.js    # Service Worker主文件
│   └── mock-data.json           # 默认模拟数据
├── src/
│   ├── utils/
│   │   └── mockManager.ts       # 模拟功能管理器
│   └── components/
│       └── MockDebugPanel/      # 调试面板组件
│           └── MockDebugPanel.vue
└── doc/
    └── 接口模拟功能使用说明.md  # 本文档
```

## 快速开始

### 1. 初始化模拟功能

在项目入口文件（如`main.ts`）中初始化模拟管理器：

```typescript
import { mockManager } from '@/utils/mockManager'

// 开发环境自动初始化（已在mockManager中配置）
// 或手动初始化
if (process.env.NODE_ENV === 'development') {
  mockManager.init().catch(console.error)
}
```

### 2. 添加调试面板（可选）

在需要调试的页面中添加调试面板组件：

```vue
<template>
  <div>
    <!-- 你的页面内容 -->
    
    <!-- 开发环境下显示调试面板 -->
    <MockDebugPanel v-if="isDevelopment" />
  </div>
</template>

<script>
import MockDebugPanel from '@/components/MockDebugPanel/MockDebugPanel.vue'

export default {
  components: { MockDebugPanel },
  computed: {
    isDevelopment() {
      return process.env.NODE_ENV === 'development'
    }
  }
}
</script>
```

### 3. 基本使用

#### 通过代码控制

```typescript
import { mockManager } from '@/utils/mockManager'

// 启用模拟功能
await mockManager.enable()

// 设置延迟时间
await mockManager.setDelay(1000)

// 启用特定接口模拟
await mockManager.addEnabledApi('bangdao.gfActivity.home.page')

// 设置模拟数据
await mockManager.setMockData('bangdao.gfActivity.home.page', {
  ret_code: 200,
  content: {
    rtn_flag: '9999',
    rtn_data: { message: '自定义模拟数据' }
  }
})

// 禁用模拟功能
await mockManager.disable()
```

#### 通过调试面板控制

1. 页面右下角会显示"Mock"浮动按钮
2. 点击按钮打开调试面板
3. 在面板中可以：
   - 开启/关闭模拟功能
   - 设置响应延迟
   - 选择要模拟的接口
   - 编辑模拟数据
   - 查看状态信息

## 配置说明

### MockConfig 接口

```typescript
interface MockConfig {
  enabled: boolean        // 是否启用模拟功能
  delay: number          // 响应延迟时间（毫秒）
  logRequests: boolean   // 是否记录请求日志
  mockAll: boolean       // 是否模拟所有接口
  enabledApis: string[]  // 启用模拟的接口列表
}
```

### 默认配置

```typescript
const DEFAULT_CONFIG = {
  enabled: false,
  delay: 500,
  logRequests: true,
  mockAll: false,
  enabledApis: []
}
```

## 模拟数据格式

### 标准响应格式

项目使用统一的响应格式，模拟数据应遵循以下结构：

```typescript
{
  "ret_code": 200,           // 网关返回码
  "content": {
    "rtn_flag": "9999",      // 业务返回码
    "rtn_msg": "成功",        // 业务返回消息
    "rtn_data": {            // 业务数据
      // 具体的业务数据
    }
  }
}
```

### 示例模拟数据

#### 登录接口模拟数据

```json
{
  "bangdao.gfActivity.common.login": {
    "ret_code": 200,
    "content": {
      "rtn_flag": "9999",
      "rtn_msg": "登录成功",
      "rtn_data": {
        "user_token": "MOCK_TOKEN_123456789",
        "userId": "MOCK_USER_001",
        "mobile": "138****8888",
        "nickname": "测试用户"
      }
    }
  }
}
```

#### 答题接口模拟数据

```json
{
  "bangdao.gfActivity.qa.extract": {
    "ret_code": 200,
    "content": {
      "rtn_flag": "9999",
      "rtn_msg": "成功",
      "rtn_data": {
        "questions": [
          {
            "questionId": "Q001",
            "questionText": "以下哪种行为最能体现低碳生活理念？",
            "questionType": "single",
            "options": [
              {
                "optionId": "A",
                "optionText": "随手关灯"
              },
              {
                "optionId": "B",
                "optionText": "长时间开空调"
              }
            ],
            "correctAnswer": "A"
          }
        ]
      }
    }
  }
}
```

### 动态模拟数据

支持使用函数生成动态模拟数据：

```typescript
// 在代码中设置动态数据
await mockManager.setMockData('bangdao.gfActivity.home.page', (requestData) => {
  return {
    ret_code: 200,
    content: {
      rtn_flag: '9999',
      rtn_data: {
        timestamp: Date.now(),
        requestId: Math.random().toString(36),
        // 根据请求参数返回不同数据
        userId: requestData.userId || 'default_user'
      }
    }
  }
})
```

## API 参考

### MockManager 类

#### 初始化方法

```typescript
// 初始化模拟管理器
await mockManager.init()
```

#### 基本控制方法

```typescript
// 启用/禁用模拟功能
await mockManager.enable()
await mockManager.disable()

// 设置延迟时间
await mockManager.setDelay(1000)

// 设置日志记录
await mockManager.setLogRequests(true)

// 设置模拟所有接口
await mockManager.setMockAll(true)
```

#### 接口管理方法

```typescript
// 设置启用的接口列表
await mockManager.setEnabledApis(['api1', 'api2'])

// 添加启用的接口
await mockManager.addEnabledApi('bangdao.gfActivity.home.page')

// 移除启用的接口
await mockManager.removeEnabledApi('bangdao.gfActivity.home.page')
```

#### 数据管理方法

```typescript
// 设置模拟数据
await mockManager.setMockData('api.method', mockData)

// 获取模拟数据
const data = mockManager.getMockData('api.method')
const allData = mockManager.getMockData()

// 删除模拟数据
await mockManager.removeMockData('api.method')
```

#### 状态和工具方法

```typescript
// 获取当前配置
const config = mockManager.getConfig()

// 获取状态信息
const status = await mockManager.getStatus()

// 清除缓存
await mockManager.clearCache()

// 重置配置
await mockManager.reset()
```

## 支持的接口列表

项目当前支持以下接口的模拟：

### 活动相关接口
- `bangdao.gfActivity.home.page` - 活动首页查询
- `bangdao.gfActivity.verify.activity.time` - 活动时间校验
- `bangdao.gfActivity.prize.query` - 奖品查询

### 用户相关接口
- `bangdao.gfActivity.ds.sms.send` - 发送验证码
- `bangdao.gfActivity.common.login` - 用户登录
- `bangdao.gfActivity.lbs.judge` - 定位接口

### 业务功能接口
- `bangdao.gfActivity.qa.extract` - 获取题目
- `bangdao.gfActivity.qa.answer` - 提交答题
- `bangdao.gfActivity.submit` - 提交任务

### 微信相关接口
- `bangdao.lifecycle.wxjsapi.signature` - 微信授权

## 调试技巧

### 1. 使用浏览器开发者工具

在浏览器开发者工具的Console中，可以直接使用全局的`mockManager`对象：

```javascript
// 查看当前配置
console.log(mockManager.getConfig())

// 快速启用模拟
await mockManager.enable()

// 查看所有模拟数据
console.log(mockManager.getMockData())
```

### 2. 网络面板查看

在浏览器的Network面板中，被模拟的请求会有特殊标识：
- Response Headers中会包含`X-Mock-Response: true`
- 响应时间会根据设置的延迟显示

### 3. 日志查看

启用日志记录后，可以在Console中看到详细的拦截日志：

```
[Mock SW] Intercepted API request: bangdao.gfActivity.home.page {...}
```

### 4. 调试面板功能

调试面板提供了完整的可视化控制：
- **基本控制**: 开关、延迟、日志等设置
- **接口配置**: 选择要模拟的接口
- **数据编辑**: 在线编辑模拟数据
- **状态监控**: 查看Service Worker状态
- **操作工具**: 刷新、清除、重置等

## 最佳实践

### 1. 开发阶段

```typescript
// 开发时启用所有接口模拟，快速开发
await mockManager.enable()
await mockManager.setMockAll(true)
await mockManager.setDelay(200) // 较短延迟
```

### 2. 测试阶段

```typescript
// 测试时选择性模拟，测试真实接口
await mockManager.enable()
await mockManager.setMockAll(false)
await mockManager.setEnabledApis([
  'bangdao.gfActivity.qa.extract',  // 模拟题目数据
  'bangdao.gfActivity.qa.answer'    // 模拟答题结果
])
await mockManager.setDelay(1000) // 模拟真实网络延迟
```

### 3. 演示阶段

```typescript
// 演示时使用稳定的模拟数据
await mockManager.enable()
await mockManager.setMockAll(true)
await mockManager.setDelay(500)
await mockManager.setLogRequests(false) // 关闭日志避免干扰
```

### 4. 数据管理

```typescript
// 为不同场景准备不同的模拟数据
const scenarios = {
  success: { rtn_flag: '9999', rtn_data: {...} },
  error: { rtn_flag: '50001', rtn_msg: '系统错误' },
  timeout: null // 不返回数据，模拟超时
}

// 根据测试需要切换场景
await mockManager.setMockData('api.method', scenarios.success)
```

## 故障排除

### 1. Service Worker 未注册

**问题**: 模拟功能不生效  
**解决**: 
- 检查浏览器是否支持Service Worker
- 确认HTTPS环境（localhost除外）
- 查看浏览器Console是否有错误信息

### 2. 模拟数据格式错误

**问题**: 接口返回异常  
**解决**:
- 检查模拟数据是否符合项目响应格式
- 使用JSON验证工具检查数据格式
- 查看Network面板的响应内容

### 3. 配置不生效

**问题**: 配置更改后不生效  
**解决**:
- 刷新页面重新加载Service Worker
- 清除浏览器缓存
- 使用调试面板的"清除缓存"功能

### 4. 性能问题

**问题**: 页面响应变慢  
**解决**:
- 减少响应延迟设置
- 关闭不必要的日志记录
- 只模拟必要的接口

## 注意事项

1. **环境限制**: 建议只在开发和测试环境使用，生产环境应禁用
2. **数据安全**: 模拟数据存储在本地，注意不要包含敏感信息
3. **浏览器兼容**: 需要支持Service Worker的现代浏览器
4. **网络环境**: HTTPS环境下才能正常工作（localhost除外）
5. **缓存管理**: 定期清理缓存避免数据冲突

## 相关文档

- [技术栈基本信息](./技术栈基本信息.md)
- [网络接口封装使用说明](./网络接口封装使用说明.md)
- [全局组件说明文档](./全局组件说明文档.md)
- [数据流使用说明文档](./数据流使用说明文档.md)
- [复杂业务逻辑说明](./复杂业务逻辑说明.md)
