# 全局组件说明文档

## 概述

项目包含多个可复用的全局组件，涵盖了页面布局、用户交互、数据展示等各个方面。这些组件采用Vue 2 + TypeScript + Vue Class Component的技术栈开发，具有良好的类型安全性和可维护性。

## 组件分类

### 1. 布局组件
- **Background** - 页面背景布局组件
- **InitMask** - 页面加载遮罩组件

### 2. 交互组件
- **Modal** - 通用弹窗组件
- **BackButton** - 返回按钮组件
- **IframeBack** - iframe页面返回按钮

### 3. 业务组件
- **PuttingList** - 图文投放列表组件
- **TaskList** - 任务列表组件
- **globalAudio** - 全局音频控制组件

### 4. 弹窗组件
- **answerOtherModal** - 答题其他弹窗
- **answerTipsModal** - 答题提示弹窗
- **ModalArea** - 区域选择弹窗

## 详细组件说明

### 1. Background 组件

#### 功能描述
页面背景布局组件，支持多种布局模式、透明标题栏、刘海屏适配等功能。

#### Props 参数
```typescript
interface BackgroundProps {
  layoutType?: string          // 布局类型，默认'onePageTop'
  pageHeight?: number          // 页面高度
  bgImage?: string | string[]  // 背景图片
  bgColor?: string            // 背景颜色，默认'#fff'
  liuhaiHeight?: number       // 刘海高度，默认-1
  isTransparentTitle?: boolean // 是否透明标题栏，默认false
  bgTransparentTitleToLiuhaiShow?: boolean // 是否显示刘海背景
  bgTransparentTitleToLiuhai?: string      // 刘海背景图
  bgTransparentTitleToLiuhaiColor?: string // 刘海背景色
}
```

#### 布局类型说明
- **onePageTop**: 一屏贴顶布局（背景图片：750*1496，透明标题栏：750*1624）
- **imageSizeTop**: 贴顶可滚动布局（背景图片：750*1206+）
- **onePageMiddle**: 一屏居中布局（已废弃）

#### 使用示例
```vue
<template>
  <Background v-bind="bgLayout">
    <div class="page-content">
      <!-- 页面内容 -->
    </div>
  </Background>
</template>

<script>
import Background from '@/components/background/Background.vue'

export default {
  components: { Background },
  computed: {
    bgLayout() {
      return {
        layoutType: 'onePageTop',
        bgImage: 'https://example.com/bg.jpg',
        bgColor: '#f5f5f5',
        isTransparentTitle: true
      }
    }
  }
}
</script>
```

### 2. InitMask 组件

#### 功能描述
页面加载时的遮罩组件，提供loading动画和自定义文案。

#### Props 参数
```typescript
interface InitMaskProps {
  loadingColor?: string  // 加载点颜色，默认'#108ee9'
  maskBgColor?: string   // 背景颜色，默认'#fff'
  text?: string          // 加载文案，默认'加载中...'
}
```

#### 使用示例
```vue
<template>
  <div>
    <InitMask
      v-if="!isReady"
      :loadingColor="pageTab.bgColor"
      text="数据加载中..."
    />
    <!-- 页面内容 -->
  </div>
</template>

<script>
import InitMask from '@/components/mask/InitMask.vue'

export default {
  components: { InitMask },
  data() {
    return {
      isReady: false
    }
  }
}
</script>
```

### 3. Modal 组件

#### 功能描述
通用弹窗组件，基于Vant的van-popup封装，支持自定义背景、位置和关闭按钮。

#### Props 参数
```typescript
interface ModalProps {
  position?: string        // 弹窗位置，默认'center'
  bgObj?: object          // 背景图属性
  showCloseButton?: boolean // 是否显示关闭按钮，默认true
}
```

#### 特殊功能
- **滚动锁定**: 弹窗显示时自动锁定背景滚动
- **位置记忆**: 关闭弹窗时恢复原滚动位置
- **编辑环境兼容**: 在编辑环境下跳过滚动处理

#### 使用示例
```vue
<template>
  <Modal
    ref="modalRef"
    :bg-obj="{ background: dialogBg }"
    :show-close-button="false"
    @close="handleClose"
  >
    <div class="modal-content">
      <!-- 弹窗内容 -->
    </div>
  </Modal>
</template>

<script>
import Modal from '@/components/modal/Modal.vue'

export default {
  components: { Modal },
  methods: {
    showModal() {
      this.$refs.modalRef.show = true
    },
    handleClose() {
      console.log('弹窗关闭')
    }
  }
}
</script>
```

### 4. BackButton 组件

#### 功能描述
返回按钮组件，支持自定义图标、宽度和返回行为。

#### Props 参数
```typescript
interface BackButtonProps {
  image?: string   // 按钮图标，默认使用常量中的BACK图标
  width?: string   // 按钮宽度，默认'6%'
  isBack?: boolean // 是否触发自定义返回事件，默认false
}
```

#### 使用示例
```vue
<template>
  <BackButton
    :image="customBackIcon"
    width="8%"
    :isBack="true"
    @isGoBack="handleGoBack"
  />
</template>

<script>
import BackButton from '@/components/back/BackButton.vue'

export default {
  components: { BackButton },
  methods: {
    handleGoBack() {
      // 自定义返回逻辑
      this.$router.push('/home')
    }
  }
}
</script>
```

### 5. globalAudio 组件

#### 功能描述
全局音频控制组件，支持背景音乐播放、暂停和音量控制，兼容微信环境。

#### 主要功能
- **自动播放**: 支持微信环境下的自动播放
- **播放控制**: 点击切换播放/暂停状态
- **视觉反馈**: 播放时图标旋转动画
- **平台适配**: iOS和微信环境特殊处理

#### 使用示例
```vue
<template>
  <div>
    <globalAudio />
    <!-- 其他页面内容 -->
  </div>
</template>

<script>
import globalAudio from '@/components/audioComponent/globalAudio.vue'

export default {
  components: { globalAudio }
}
</script>
```

### 6. PuttingList 组件

#### 功能描述
图文投放列表组件，封装了投放数据查询、点击跳转、埋点等通用逻辑。

#### Props 参数
```typescript
interface PuttingListProps {
  standId: string        // 互动平台展位ID（必填）
  needLBS?: boolean      // 是否需要获取定位信息，默认false
  maxLength?: number     // 展示最大数量
  trackConfig?: object   // 埋点配置，默认{ eventBlock: '' }
}
```

#### Slots 插槽
- **puttingItem**: 单个投放项的作用域插槽 `{ item, handleItemClick }`
- **loading**: 加载中的插槽
- **listEmpty**: 列表为空时的插槽
- **listError**: 列表查询错误的插槽

#### 使用示例
```vue
<template>
  <PuttingList
    :standId="standId"
    :trackConfig="{ eventBlock: '首页投放' }"
    :maxLength="5"
  >
    <!-- 投放项模板 -->
    <div
      class="putting-item"
      slot="puttingItem"
      slot-scope="{ item, handleItemClick }"
      @click="handleItemClick(item)"
    >
      <img :src="item.imageUrl" />
      <div class="title">{{ item.title }}</div>
    </div>

    <!-- 加载状态 -->
    <div slot="loading">加载中...</div>

    <!-- 空状态 -->
    <div slot="listEmpty">暂无投放内容</div>

    <!-- 错误状态 -->
    <div slot="listError" slot-scope="{ errorMessage }">
      加载失败：{{ errorMessage }}
    </div>
  </PuttingList>
</template>
```

### 7. TaskList 组件

#### 功能描述
任务列表组件，封装了任务数据查询、任务完成、奖励刷新等逻辑。

#### Props 参数
```typescript
interface TaskListProps {
  standId: string        // 互动平台展位ID（必填）
  trackConfig?: object   // 埋点配置
}
```

#### Events 事件
- **afterGetTaskList**: 获取任务列表后触发
- **refreshTaskAward**: 刷新任务奖励后触发

#### Slots 插槽
- **taskItem**: 单个任务项的作用域插槽 `{ item, handleItemClick }`
- **loading**: 加载中的插槽
- **listError**: 列表查询错误的插槽

#### 使用示例
```vue
<template>
  <TaskList
    :standId="standId"
    :trackConfig="{ eventBlock: '任务列表' }"
    @afterGetTaskList="checkTaskStatus"
    @refreshTaskAward="updateAwardCount"
  >
    <!-- 任务项模板 -->
    <div
      class="task-item"
      slot="taskItem"
      slot-scope="{ item, handleItemClick }"
    >
      <div class="task-icon">
        <img :src="item.taskIcon" />
      </div>
      <div class="task-content">
        <div class="task-title">{{ item.taskTitle }}</div>
        <div class="task-desc">{{ item.taskDesc }}</div>
      </div>
      <div
        :class="['task-btn', item.taskStatus]"
        @click="handleItemClick(item)"
      >
        {{ item.btnText }}
      </div>
    </div>
  </TaskList>
</template>
```

### 8. answerOtherModal 组件

#### 功能描述
答题相关的其他弹窗组件，用于显示答题结果、提示信息等。

#### Props 参数
```typescript
interface AnswerOtherModalProps {
  pageTab: object  // 页面配置对象
}
```

#### Events 事件
- **jumpOperation**: 跳转操作
- **backOperation**: 返回操作

#### 方法
- **open(obj)**: 打开弹窗并传入显示内容

#### 使用示例
```vue
<template>
  <answerOtherModal
    ref="answerOtherRef"
    :page-tab="pageTab"
    @jumpOperation="continueLevel"
    @backOperation="backChallenge"
  />
</template>

<script>
export default {
  methods: {
    showAnswerResult(result) {
      this.$refs.answerOtherRef.open({
        title: '答题结果',
        content: result.message,
        leftText: '重新答题',
        rightText: '继续闯关'
      })
    }
  }
}
</script>
```

### 9. answerTipsModal 组件

#### 功能描述
答题提示弹窗组件，用于显示答题相关的提示信息。

#### Props 参数
```typescript
interface AnswerTipsModalProps {
  pageTab: object  // 页面配置对象
}
```

#### Events 事件
- **jumpOperation**: 跳转操作，传递操作类型参数

#### 方法
- **open(obj)**: 打开弹窗并传入显示内容

#### 使用示例
```vue
<template>
  <answerTipsModal
    ref="tipsModalRef"
    :page-tab="pageTab"
    @jumpOperation="handleJumpOperation"
  />
</template>

<script>
export default {
  methods: {
    showTips() {
      this.$refs.tipsModalRef.open({
        title: '提示',
        content: '请仔细阅读题目后再作答',
        btnText: '我知道了',
        type: 'continue'
      })
    },
    handleJumpOperation(type) {
      if (type === 'continue') {
        // 继续答题逻辑
      }
    }
  }
}
</script>
```

## 组件开发规范

### 1. 命名规范
- **组件文件**: 使用PascalCase命名，如`BackButton.vue`
- **组件类名**: 与文件名保持一致
- **Props**: 使用camelCase命名
- **Events**: 使用camelCase命名

### 2. 类型定义
```typescript
// 使用Vue Property Decorator
@Component({
  components: { /* 子组件 */ }
})
export default class ComponentName extends Vue {
  @Prop({ type: String, default: '' }) propName!: string
  @Emit() eventName() { return }
}
```

### 3. 样式规范
- 使用`scoped`样式避免污染
- 使用Less预处理器
- 采用rem单位适配移动端
- 遵循BEM命名规范

### 4. 插槽使用
```vue
<!-- 具名插槽 -->
<slot name="header"></slot>

<!-- 作用域插槽 -->
<slot name="item" :item="item" :index="index"></slot>

<!-- 默认插槽 -->
<slot></slot>
```

## 最佳实践

### 1. 组件复用
- 优先使用现有全局组件
- 新组件开发前先评估复用性
- 通过Props和Slots提高组件灵活性

### 2. 性能优化
- 合理使用v-if和v-show
- 避免在模板中使用复杂计算
- 使用computed缓存计算结果

### 3. 错误处理
- 为异步操作添加错误处理
- 提供友好的错误提示
- 使用try-catch包装可能出错的代码

### 4. 可访问性
- 为图片添加alt属性
- 使用语义化HTML标签
- 确保键盘导航可用

### 10. IframeBack 组件

#### 功能描述
iframe页面专用的返回按钮组件，支持覆盖模式和嵌入模式。

#### Props 参数
```typescript
interface IframeBackProps {
  backIcon?: string    // 返回按钮图标
  isCover?: boolean    // 是否覆盖模式，默认true
  bgColor?: string     // 背景颜色，默认'#ffffff'
  title?: string       // 标题文本
  pathName?: string    // 指定返回页面name
  isClick?: boolean    // 是否触发点击事件，默认false
}
```

#### 使用示例
```vue
<template>
  <IframeBack
    :isCover="false"
    :isClick="true"
    :back-icon="customIcon"
    title="返回首页"
    @click="handleBack"
  />
</template>

<script>
import IframeBack from '@/components/IframeBack/index.vue'

export default {
  components: { IframeBack },
  methods: {
    handleBack() {
      // 自定义返回逻辑
    }
  }
}
</script>
```

### 11. ModalArea 组件

#### 功能描述
区域选择弹窗组件，简化版的Modal组件，不包含背景图片设置。

#### Props 参数
```typescript
interface ModalAreaProps {
  position?: string        // 弹窗位置，默认'center'
  showCloseButton?: boolean // 是否显示关闭按钮，默认true
}
```

#### 使用示例
```vue
<template>
  <ModalArea
    ref="areaModalRef"
    :show-close-button="true"
    @close="handleAreaClose"
  >
    <div class="area-content">
      <!-- 区域选择内容 -->
    </div>
  </ModalArea>
</template>
```

## 组件状态管理

### 1. 音频组件状态
音频组件使用Vuex管理全局音乐状态：

```typescript
// store/modules/music.ts
export default {
  namespaced: true,
  state: {
    musicFlag: false  // 音乐播放状态
  },
  mutations: {
    setMusicFlag(state, flag) {
      state.musicFlag = flag
    }
  }
}
```

### 2. 图片资源状态
全局图片资源通过Vuex统一管理：

```typescript
// store/index.ts
export default {
  state: {
    IMAGE: {
      BACK: 'https://example.com/back.png',
      CLOSE: 'https://example.com/close.png'
      // 其他图片资源
    }
  }
}
```

## 组件通信模式

### 1. 父子组件通信
```vue
<!-- 父组件 -->
<template>
  <ChildComponent
    :prop-data="parentData"
    @child-event="handleChildEvent"
  />
</template>

<!-- 子组件 -->
<script>
@Component({})
export default class ChildComponent extends Vue {
  @Prop() propData!: any
  @Emit('child-event')
  emitToParent(data: any) {
    return data
  }
}
</script>
```

### 2. 兄弟组件通信
通过父组件或Vuex进行通信：

```typescript
// 通过Vuex
this.$store.commit('setGlobalData', data)

// 通过事件总线
this.$bus.$emit('global-event', data)
this.$bus.$on('global-event', this.handleEvent)
```

### 3. 跨层级通信
使用provide/inject或Vuex：

```typescript
// 祖先组件
@Component({
  provide() {
    return {
      globalMethod: this.globalMethod
    }
  }
})

// 后代组件
@Component({
  inject: ['globalMethod']
})
```

## 组件测试

### 1. 单元测试示例
```typescript
import { shallowMount } from '@vue/test-utils'
import BackButton from '@/components/back/BackButton.vue'

describe('BackButton.vue', () => {
  it('renders correctly', () => {
    const wrapper = shallowMount(BackButton, {
      propsData: {
        image: 'test-icon.png',
        width: '10%'
      }
    })
    expect(wrapper.find('img').exists()).toBe(true)
  })

  it('emits isGoBack when isBack is true', async () => {
    const wrapper = shallowMount(BackButton, {
      propsData: { isBack: true }
    })
    await wrapper.find('.image').trigger('click')
    expect(wrapper.emitted('isGoBack')).toBeTruthy()
  })
})
```

### 2. 集成测试
```typescript
import { mount } from '@vue/test-utils'
import Modal from '@/components/modal/Modal.vue'

describe('Modal.vue', () => {
  it('shows and hides correctly', async () => {
    const wrapper = mount(Modal)

    // 显示弹窗
    wrapper.vm.show = true
    await wrapper.vm.$nextTick()
    expect(wrapper.find('.popup').isVisible()).toBe(true)

    // 隐藏弹窗
    wrapper.vm.show = false
    await wrapper.vm.$nextTick()
    expect(wrapper.find('.popup').isVisible()).toBe(false)
  })
})
```

## 性能优化建议

### 1. 组件懒加载
```typescript
// 路由级别懒加载
const AsyncComponent = () => import('@/components/HeavyComponent.vue')

// 组件级别懒加载
export default {
  components: {
    HeavyComponent: () => import('@/components/HeavyComponent.vue')
  }
}
```

### 2. 条件渲染优化
```vue
<template>
  <!-- 使用v-if避免不必要的渲染 -->
  <ExpensiveComponent v-if="shouldShow" />

  <!-- 使用v-show适合频繁切换的场景 -->
  <SimpleComponent v-show="isVisible" />
</template>
```

### 3. 事件监听优化
```typescript
// 组件销毁时清理事件监听
beforeDestroy() {
  window.removeEventListener('resize', this.handleResize)
  this.$bus.$off('global-event', this.handleGlobalEvent)
}
```

## 常见问题

### 1. 组件引入问题
**问题**: 组件引入后无法使用
**解决**: 检查组件路径和注册方式

```typescript
// 正确的组件引入方式
import BackButton from '@/components/back/BackButton.vue'

@Component({
  components: { BackButton }
})
```

### 2. 样式冲突
**问题**: 组件样式被覆盖
**解决**: 使用scoped样式或提高选择器优先级

```vue
<style scoped lang="less">
.component-class {
  // 组件专有样式
}
</style>

<!-- 或使用深度选择器 -->
<style scoped lang="less">
::v-deep .van-popup {
  z-index: 999 !important;
}
</style>
```

### 3. 事件传递
**问题**: 子组件事件无法传递到父组件
**解决**: 检查@Emit装饰器使用和事件监听

```typescript
// 子组件
@Emit('custom-event')
handleClick() {
  return { data: 'event data' }
}

// 父组件
<ChildComponent @custom-event="handleCustomEvent" />
```

### 4. 类型错误
**问题**: TypeScript类型检查报错
**解决**: 完善Props类型定义和接口声明

```typescript
interface ComponentProps {
  title: string
  count?: number
}

@Prop({ type: String, required: true }) title!: string
@Prop({ type: Number, default: 0 }) count!: number
```

### 5. 弹窗滚动问题
**问题**: 弹窗显示时背景仍可滚动
**解决**: Modal组件已内置滚动锁定功能

```typescript
// Modal组件自动处理滚动锁定
@Watch("show")
visibleChange(val) {
  const $mainPage = document.querySelector("#app") as HTMLElement;
  if (val) {
    this.top = window.scrollY;
    $mainPage.style.position = "fixed";
    $mainPage.style.top = -this.top + "px";
  } else {
    $mainPage.style.position = "";
    $mainPage.style.top = "";
    window.scrollTo(0, this.top);
  }
}
```

## 相关文档

- [技术栈基本信息](./技术栈基本信息.md)
- [网络接口封装使用说明](./网络接口封装使用说明.md)
- [数据流使用说明文档](./数据流使用说明文档.md)
- [复杂业务逻辑说明](./复杂业务逻辑说明.md)
