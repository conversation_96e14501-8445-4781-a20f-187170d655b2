# 复杂业务逻辑说明

## 概述

本文档详细说明项目中的复杂业务逻辑实现，包括用户认证、答题系统、任务管理、抽奖机制、微信分享、视频任务等核心功能模块。这些业务逻辑涉及多个系统的交互，包含复杂的状态管理和异步处理。

## 业务模块架构

### 整体业务流程图
```
用户进入 → 授权登录 → 首页展示 → 任务系统 → 答题闯关 → 抽奖奖励 → 分享传播
    ↓         ↓         ↓         ↓         ↓         ↓         ↓
  页面加载   手机验证   活动信息   任务完成   题目答题   奖品发放   微信分享
    ↓         ↓         ↓         ↓         ↓         ↓         ↓
  图片预加载 RSA加密   定时校验   埋点统计   结果判断   次数管理   签名获取
```

## 1. 用户认证与登录系统

### 1.1 登录流程

#### 核心实现 (src/pages/login/components/login/login.vue)

```typescript
// 登录主流程
@jumpTime()
async handleLogin() {
  buriedPoint('点击登陆')
  if (!this.verifyLogin()) return
  
  const { storageName, errorList } = this.configData
  const {
    source,
    pubms_code,
    activity_id: relate_id,
    app_id,
    item_type,
  } = this.pageTab
  
  // 调用登录接口
  const [res, err] = await apis.login({
    app_id,
    pubms_code,
    source,
    relate_id,
    act_id: relate_id,
    item_type: item_type,
    busi_type: item_type,
    is_rsa: 'T',
    sms_code: this.code,
    mobile: encrypt(this.mobile),  // RSA加密手机号
  })
  
  if (err) {
    const { rtn_flag, rtn_msg } = err
    const flag = errorList.find(item => item.code === rtn_flag)?.codeText
    return this.$toast(flag ?? rtn_msg ?? '系统繁忙，请稍候')
  }
  
  // 登录成功，保存token并跳转
  localStorage.setItem(storageName, res.user_token)
  buriedPoint('登录成功')
  await this.$router.replace('/')
}
```

#### 验证码发送
```typescript
// 发送验证码
async sendCode() {
  if (!this.verifyMobile()) return
  
  const [res, err] = await apis.sendCode({
    mobile: this.mobile,
    app_id: this.pageTab.app_id,
    pubms_code: this.pageTab.pubms_code,
    source: this.pageTab.source
  })
  
  if (err) {
    return this.$toast(err.rtn_msg || '发送失败')
  }
  
  // 启动倒计时
  this.startCountdown()
}
```

#### 安全特性
- **RSA加密**: 手机号使用RSA加密传输
- **验证码校验**: 短信验证码验证
- **防重复提交**: 使用@jumpTime装饰器防抖
- **错误处理**: 统一错误码映射和提示

### 1.2 授权系统

#### 微信授权 (public/sdk-funeng/auth-weixin.js)
```javascript
BangdaoSeSdk.handleAuth = function() {
  return new Promise(function(resolve) {
    const authCode = BangdaoSeSdk.getQueryString('authCode')
    const authTokenFromUrl = BangdaoSeSdk.getQueryString('authToken')
    
    if (authCode) {
      // 微信授权完成，获取授权回调返回的authCode进行内部授权换取token
      window._BD_FUNC_authRequest &&
        window._BD_FUNC_authRequest({
          authCode: authCode,
          channelCode: BangdaoSeSdk.getQueryString('appId'),
          scope: BangdaoSeSdk.getQueryString('scope'),
        }, function([authToken, error]) {
          if (error) {
            alert('授权失败，请重新访问!')
          } else {
            resolve({ authToken })
          }
        })
    }
  })
}
```

## 2. 答题系统

### 2.1 答题流程

#### 核心实现 (src/pages/answer/components/Answer/Answer.vue)

```typescript
// 获取题目
async getQuestions() {
  const [res, err] = await apis.getQuestion({
    activity_id: this.pageTab.activityId,
    app_id: this.pageTab.appId,
    token: localStorage.getItem(this.configData.storageName)
  })
  
  if (err) {
    return this.showErr(err)
  }
  
  this.questionList = res.rtn_data.questions
  this.totalQuestions = this.questionList.length
}

// 提交答题
async submitAnswer() {
  this.$toast.loading('提交中...')
  
  const [res, err] = await apis.addAnswer({
    source: this.pageTab.source,
    pubms_code: this.pageTab.pubms_code,
    app_id: this.pageTab.app_id,
    activity_id: this.pageTab.activityId,
    item_type: this.pageTab.item_type,
    token: localStorage.getItem(this.configData.storageName),
    extend_params: JSON.stringify(this.selectList),  // 答案列表
  })
  
  this.$toast.clear()
  this.done = true
  
  if (err) {
    const { rtn_msg, rtn_flag } = err
    const flag = this.pageTab.errorList.find(item => {
      return item.code == rtn_flag
    })
    showErrorMsg({
      showType: 'alert',
      message: flag?.codeText || rtn_msg || '问卷提交失败',
      oKBtnHandle: () => {
        this.stopBack()
      },
    })
    return
  }
  
  if (res) {
    const {
      rtn_data: { correctCount, grantResults, isUp, isComplete, qaNum },
    } = res
    this.mobile = res?.rtn_data?.mobile ?? ''
    this.qNum = qaNum
    this.isComplete = isComplete
    this.initAward(correctCount, grantResults, isUp, isComplete)
  }
}
```

### 2.2 答题结果处理

```typescript
// 初始化奖励结果
initAward(correctCount: any, grantResults: any, isUp: any, isComplete: any) {
  // 未中奖处理
  if (!isUp) {
    buriedPoint('未中奖弹窗')
    return this.answerOtherRef.open({
      title: '闯关失败',
      content: this.pageTab.noPrizeContent,
      leftText: '返回首页',
      rightText: '继续闯关',
    })
  }
  
  // 中奖处理
  if (grantResults && grantResults.length > 0) {
    const prize = grantResults[0]?.draw_prize_infos
    if (!prize && grantResults[0]?.sub_code) {
      // 无奖品但有次数
      this.noPrizeDialogBg = this.isComplete 
        ? this.pageTab.imgList.noCompleteDialogBg 
        : this.pageTab.imgList.noPrizeDialogBg
      this.noPrizeContent = this.pageTab.noAwardContent
      this.isPassChance = false
      
      if (this.isComplete) {
        this.showComplete()
        return
      }
      this.showNoPrize = true
      buriedPoint('领奖失败')
      return
    }
    
    // 有奖品处理
    if (prize && prize.length > 0) {
      this.prizeInfo = prize[0]
      this.showPrize = true
      buriedPoint('中奖弹窗')
    }
  }
}

// 继续闯关
continueLevel() {
  buriedPoint('继续挑战')
  
  this.done = false
  this.showNoPrize = false
  this.showPrize = false
  
  if ((this.qNum <= 0 && !this.isComplete) || this.isComplete) {
    this.$toast(this.isComplete ? '您已通关，无法继续答题' : '次数不足，快去做任务获得闯关次数吧。')
    setTimeout(() => {
      this.backChallenge()
    }, 3000)
    return
  }
  
  // 重置题目和选项状态
  this.currentQuestion = 1
  this.select = ''
  this.selectList = []
  this.resultError = false
  this.showAnswer = false
  this.submitClick = false
  
  // 清除计时器
  if (this.nextQuestionTimer) {
    clearTimeout(this.nextQuestionTimer)
    this.nextQuestionTimer = null
  }
  
  // 重新获取题目
  this.getQuestions()
}
```

## 3. 任务管理系统

### 3.1 任务列表组件 (src/components/TaskList/TaskList.vue)

```typescript
// 任务按钮点击处理
async onTaskClick(taskItem) {
  if (this.taskClickLoading || this.taskListLoading) return
  this.taskClickLoading = true
  
  const { taskId, taskStatus } = taskItem
  
  if (taskStatus === EnumTaskStatus.UNFINISH) {
    // 任务未完成，点击去完成
    this.taskTrackEvent('PutClick', taskItem)
    await this.completeTaskHandle(taskItem)
  } else if (taskStatus === EnumTaskStatus.NOT_GET) {
    // 任务已完成未领取，点击领取
    await this.getAwardByTask({ taskId })
    await this.getTaskList()
    this.$emit('refreshTaskAward')
    this.taskTrackEvent('PutClick', taskItem)
  } else if (taskStatus === EnumTaskStatus.FINISHED) {
    // 任务已完成，已领取
    this.taskTrackEvent('PutClick', taskItem)
  }
  
  this.taskClickLoading = false
}

// 完成任务处理
async completeTaskHandle(taskItem) {
  const { taskId, btnOperation, link, duration, param } = taskItem
  
  switch (btnOperation) {
    case EnumTaskBtnOperation.LINK:
      // 跳转链接
      window._BD_FUNC_linkHandle && window._BD_FUNC_linkHandle(link)
      await this.completeTask({ taskId, param })
      await this.getTaskList()
      this.$emit('refreshTaskAward')
      break
      
    case EnumTaskBtnOperation.BROWSE:
      // 浏览任务，需要等待指定时间
      this.browserTaskDoing = true
      this.browserTaskTimeout = setTimeout(async () => {
        this.browserTaskDoing = false
        await this.completeTask({ taskId, param }, false)
        await this.getTaskList()
        this.$emit('refreshTaskAward')
      }, duration * 1000)
      window._BD_FUNC_linkHandle && window._BD_FUNC_linkHandle(link)
      break
  }
}
```

### 3.2 任务类型处理 (src/pages/index/components/ActCommon/ActCommon.vue)

```typescript
// 任务处理配置
@jumpTime()
async handleTask(item) {
  if (item.completionStatus === 2) return
  
  const taskConfig = {
    2: () => {
      buriedPoint('点击去报名')
      window.location.href = this.pageTab.linkUrl
    },
    4: () => {
      buriedPoint('点击查看节电视频任务')
      this.$router.push({
        path: '/taskVideo'
      })
    },
    3: () => {
      buriedPoint('点击查看转发活动')
      this.finishTask(item)
    },
  }
  taskConfig[item.taskType]()
}

// 完成任务/领取接口
@jumpTime()
async finishTask(item) {
  const { completionStatus, id, taskType } = item
  const [, err] = await apis.addSubmit({
    activity_id: this.pageTab.activityId,
    app_id: this.pageTab.appId,
    pubms_code: this.pageTab.pubms_code,
    item_type: this.pageTab.item_type,
    token: localStorage.getItem(this.pageTab.storageName),
    append_type: ''
  })
  
  if (err) {
    return this.showErr(err)
  }
  
  if (taskType == '3') {
    await this.homePage()
    this.scrollToTop()
    setTimeout(() => {
      this.modalShareRef.show = true
    }, 1000)
  }
}
```

## 4. 视频任务系统

### 4.1 视频播放与完成 (src/pages/videoTask/components/ActCommon/ActCommon.vue)

```typescript
mounted() {
  this.videoUrl = this.videoList[this.videoIndex]?.videoUrl
  if (!this.videoUrl) return
  
  this.playCallBack = () => {
    this.playBack()
  }
  
  this.$nextTick(() => {
    this.videoRef.load()
    // 监听视频播放结束事件
    this.videoRef.addEventListener('ended', this.playCallBack, false)
  })
}

// 视频播放完成回调
async playBack() {
  await this.finishTask()
}

// 完成视频任务
async finishTask() {
  const [res, err] = await apis.addSubmit({
    activity_id: this.pageTab.activityId,
    app_id: this.pageTab.appId,
    pubms_code: this.pageTab.pubms_code,
    item_type: this.pageTab.item_type,
    token: localStorage.getItem(this.configData.storageName),
    append_type: 'VEDIO'  // 视频任务标识
  })
  
  this.done = true
  if (err) {
    const { rtn_flag, rtn_msg, rtn_data } = err
    const describe = this.pageTab.errorList.find(item => item.code === rtn_flag)?.codeText
    return this.$toast(describe ?? rtn_msg ?? '系统繁忙，请稍候')
  }
  
  if (res) {
    const { rtn_data: { isComplete } } = res
    this.isComplete = isComplete
    this.answerOtherRef.open({
      content: "恭喜获得1次闯关机会",
      leftText: '返回首页',
      rightText: '继续闯关'
    })
  }
}
```

## 5. 抽奖系统

### 5.1 抽奖接口 (src/apis/activity.ts)

```typescript
// 抽奖接口
export function luckDraw(
  params: IActCommonRequestParams
): Promise<[ILuckDrawResponse, ILuckDrawErrorResponse]> {
  return new Promise(resolve => {
    window._BD_FUNC_luckDraw &&
      window._BD_FUNC_luckDraw(params, function(res) {
        resolve(res)
      })
  })
}

// 抽奖机会查询
export function queryAwardCount(
  params: IActCommonRequestParams
): Promise<[IQueryAwardCountSuccessResponse, IErrorResponse]> {
  return new Promise(resolve => {
    window._BD_FUNC_queryAwardCount &&
      window._BD_FUNC_queryAwardCount(params, function(res) {
        resolve(res)
      })
  })
}
```

### 5.2 抽奖流程处理

```typescript
// 抽奖处理
async handleLuckDraw() {
  // 检查抽奖次数
  const [countResult, countError] = await queryAwardCount({
    activityId: this.activityId
  })
  
  if (countError || countResult.leftCount <= 0) {
    return this.$toast('抽奖次数不足')
  }
  
  // 执行抽奖
  const [prizeResult, prizeError] = await luckDraw({
    activityId: this.activityId
  })
  
  if (prizeError) {
    const { code, message } = prizeError
    switch (code) {
      case 'actEnd':
        return this.$toast('活动已结束')
      case 'noPrize':
        return this.showNoPrizeDialog()
      case 'noDrawTimes':
        return this.$toast('抽奖次数不足')
      default:
        return this.$toast(message || '抽奖失败')
    }
  }
  
  // 抽奖成功，显示奖品
  if (prizeResult && prizeResult.prizeList.length > 0) {
    this.showPrizeDialog(prizeResult.prizeList[0])
  }
}
```

## 6. 微信分享系统

### 6.1 分享配置 (src/store/modules/picture.ts)

```typescript
// 微信分享初始化
async resetWXShare({ commit, dispatch, state }, payload) {
  const { title, content, iconUrl, shareUrl } = state.shareCfg
  
  // 获取签名URL（iOS兼容性处理）
  const signUrl = getSignUrl()
  const [res, err] = await apis.getWX({ url: signUrl })
  
  if (err) return
  
  if (res && res.content) {
    const {
      timestamp = '',
      nonce_str: nonceStr = '',
      signature = '',
    } = res.content
    
    // 配置微信JS-SDK
    wx.config({
      debug: false,
      appId: 'wxd26f4d35936b226d',
      timestamp,
      nonceStr,
      signature,
      jsApiList: [
        'updateAppMessageShareData',
        'updateTimelineShareData',
        'onMenuShareWeibo',
        'hideAllNonBaseMenuItem',
        'showMenuItems',
      ],
    })

    wx.ready(() => {
      setTimeout(() => {
        // 显示指定的分享菜单
        wx.showMenuItems({
          menuList: ['menuItem:share:appMessage', 'menuItem:share:timeline'],
        })
      }, 400)
      
      // 分享给朋友
      wx.updateAppMessageShareData({
        title,
        desc: content,
        link: shareUrl,
        imgUrl: iconUrl,
        success: function() {
          commit('setShareOk', true)
        },
      })
      
      // 分享到朋友圈
      wx.updateTimelineShareData({
        title,
        link: shareUrl,
        imgUrl: iconUrl,
        success: function() {
          // 设置成功
        },
      })
    })
  }
}
```

### 6.2 iOS兼容性处理

```typescript
// 获取签名URL（iOS兼容性处理）
function getSignUrl(): string {
  if (isiOS()) {
    const signUrl = localStorage.getItem('signUrl')
    return signUrl || window.location.href.split('#')[0]
  }
  return window.location.href.split('#')[0]
}

// 判断是否为iOS
function isiOS(): boolean {
  const ua = navigator.userAgent
  return !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
}
```

## 7. 性能优化机制

### 7.1 图片预加载 (src/utils/imageHandle.ts)

```typescript
// 图片加载完成监听
export function imageLoadedHandle(
  imgUrls: string[] | string,
  callback: () => void
) {
  return new Promise<void>(resolve => {
    const imgUrlList = (Array.isArray(imgUrls) ? imgUrls : [imgUrls]).filter(
      item => !!item
    )
    const len = imgUrlList.length
    
    if (len === 0) {
      if (callback && typeof callback === 'function') {
        callback()
      }
      resolve()
      return
    }
    
    let n = 0
    for (let i = 0; i < len; i++) {
      if (imgUrlList[i]) {
        const img = new Image()
        img.onload = function onload() {
          this.onload = null
          n++
          if (n === len) {
            if (callback && typeof callback === 'function') {
              callback()
            }
            resolve()
          }
        }
        img.src = imgUrlList[i]
      } else {
        n++
      }
    }
  })
}

// 图片预加载
export function preLoadImage(imgList: string[]) {
  const imgs = imgList || []
  
  const loop = function loops(i) {
    const img = imgs[i]
    setTimeout(() => {
      new Image().src = img
    }, 200 * i)
  }
  
  for (let i = 0, len = imgs.length; i < len; i++) {
    loop(i)
  }
}
```

### 7.2 防抖处理 (src/utils/debounce.ts)

```typescript
// Promise节流装饰器
function PromiseThrottle(time = 200, delayTime = 0) {
  return function (target, propertyKey, descriptor) {
    const original = descriptor.value
    const throttleFn = function () {
      let status = ""
      const execFn = async function (...args) {
        if (status === "lock") {
          return
        }
        status = "lock"
        try {
          const data = await Promise.all([
            original.call(this, ...args),
            new Promise((resolve) => {
              setTimeout(resolve, time)
            }),
          ])
          return data[0]
        } finally {
          setTimeout(() => {
            status = ""
          }, delayTime)
        }
      }
      return execFn
    }
    descriptor.value = throttleFn()
    return descriptor
  }
}

// 页面跳转装饰器
function jumpTime(delayTime = 200) {
  return PromiseThrottle(0, delayTime)
}
```

## 8. 埋点统计系统

### 8.1 通用埋点 (src/utils/common.ts)

```typescript
// 埋点统计
export function buriedPoint(eventName: string, params?: any) {
  try {
    // 通用埋点
    if (window._BD_FUNC_trackEvent) {
      window._BD_FUNC_trackEvent(eventName, params)
    }
    
    // 品牌埋点
    if (window._BD_FUNC_trackEventPinpai) {
      window._BD_FUNC_trackEventPinpai('action', {
        eventBlock: '活动页面',
        eventType: eventName,
        ...params
      })
    }
  } catch (error) {
    console.error('埋点统计失败:', error)
  }
}
```

### 8.2 任务埋点处理

```typescript
// 任务埋点统一处理方法
taskTrackEvent(trackEventType, taskItem) {
  const { eventBlock, isPinPai } = this.trackConfig
  const {
    standId,
    puttingId,
    taskId,
    taskStatus,
    taskName = '',
    extendCfg = {},
  } = taskItem
  
  if (isPinPai) {
    window._BD_FUNC_trackEventPinpai &&
      window._BD_FUNC_trackEventPinpai('task', {
        eventBlock,
        eventType:
          trackEventType === 'PutExpo'
            ? '曝光'
            : taskStatus === EnumTaskStatus.UNFINISH
            ? '去完成'
            : taskStatus === EnumTaskStatus.NOT_GET
            ? '去领取'
            : '已完成',
        taskName: taskName,
        taskId,
        eventCode: extendCfg?.eventCode || '',
        brandName: extendCfg?.brandName || '',
        extendInfo: `${standId},${puttingId}`,
      })
  }
}
```

## 9. 错误处理与用户体验

### 9.1 统一错误处理

```typescript
// 显示错误信息
showErr(err: any) {
  const { rtn_flag, rtn_msg } = err
  const errorItem = this.pageTab.errorList.find(item => item.code === rtn_flag)
  const message = errorItem?.codeText || rtn_msg || '系统繁忙，请稍候'
  
  showErrorMsg({
    showType: 'alert',
    title: '提示',
    message,
    okBtnText: '确定',
    oKBtnHandle: () => {
      // 错误处理回调
    }
  })
}
```

### 9.2 加载状态管理

```typescript
// 页面初始化
protected async created() {
  // 图片加载完成执行callback
  await imageLoadedHandle([this.pageTab.bgImage], async () => {
    await this.getQuestions()
    this.isReady = true  // 设置页面就绪状态
  })
  // 图片预加载
  preLoadImage(Object.values(this.pageTab.imgList))
}
```

## 10. 最佳实践总结

### 10.1 异步处理
- 使用async/await处理异步操作
- 统一的错误处理机制
- Promise包装器统一返回格式

### 10.2 性能优化
- 图片预加载减少白屏时间
- 防抖处理避免重复操作
- 懒加载和按需加载

### 10.3 用户体验
- Loading状态提示
- 友好的错误提示
- 流畅的页面跳转

### 10.4 数据安全
- RSA加密敏感数据
- Token认证机制
- 参数校验和过滤

### 10.5 兼容性处理
- iOS微信分享兼容
- 不同平台授权适配
- 浏览器兼容性处理

## 相关文档

- [技术栈基本信息](./技术栈基本信息.md)
- [网络接口封装使用说明](./网络接口封装使用说明.md)
- [全局组件说明文档](./全局组件说明文档.md)
- [数据流使用说明文档](./数据流使用说明文档.md)
