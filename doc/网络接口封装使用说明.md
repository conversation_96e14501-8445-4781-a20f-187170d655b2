# 网络接口封装使用说明

## 概述

项目采用分层的网络请求架构，提供了完整的HTTP请求封装、错误处理、加密解密和业务API封装。整个网络层分为三个主要部分：

1. **基础请求层** (`src/utils/service/request.ts`) - 基于Axios的HTTP请求封装
2. **服务基类层** (`src/utils/service/requestService.ts`) - 业务请求的基础类
3. **业务API层** (`src/utils/service/apis.ts`) - 具体的业务接口封装

## 架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   业务组件层     │───▶│   业务API层     │───▶│   服务基类层     │
│   (Components)  │    │   (apis.ts)     │    │(requestService) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                               ┌─────────────────┐
                                               │   基础请求层     │
                                               │   (request.ts)  │
                                               └─────────────────┘
                                                       │
                                               ┌─────────────────┐
                                               │     Axios       │
                                               │   (HTTP Client) │
                                               └─────────────────┘
```

## 基础请求层

### 1. 核心配置 (src/utils/service/request.ts)

#### 基本配置
```typescript
const http = axios.create({
  baseURL: gateway,           // 网关地址
  withCredentials: true,      // 携带凭证
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
})
```

#### 网关地址配置
```typescript
// 根据环境自动切换网关
export const gateway = isTestEnv
  ? 'https://test-napi.bangdao-tech.com/gateway.do'    // 测试环境
  : 'https://napi.bangdao-tech.com/gateway.do'         // 生产环境
```

### 2. 请求参数配置

#### 基本参数
```typescript
interface RequestParams {
  handleErr?: 'all' | 'gateway' | 'none'  // 错误处理方式
  encrypt?: boolean                        // 是否加密
  defaultErrMsg?: string                   // 默认错误信息
  spErrList?: string[]                     // 特殊错误码列表
  data?: object                           // 请求数据
  noLoading?: boolean                     // 是否显示loading
  gateway?: string                        // 自定义网关地址
}
```

#### 错误处理方式说明
- **`'all'`** (默认): 统一处理所有错误码，包括网关错误和业务错误
- **`'gateway'`**: 只处理网关错误，业务错误由调用方处理
- **`'none'`**: 不统一处理错误码，直接返回原始响应

### 3. 使用方式

#### 直接使用基础请求
```typescript
import request from '@/utils/service/request'

// GET请求
const response = await request.get({
  data: { 
    method: 'api.method.name',
    param1: 'value1'
  },
  handleErr: 'all',
  encrypt: true
})

// POST请求
const response = await request.post({
  data: { 
    method: 'api.method.name',
    param1: 'value1'
  }
})
```

## 服务基类层

### 1. requestService 基类

#### 接口定义
```typescript
interface IRequestOptions {
  readonly data: IData                    // 请求数据
  readonly handleErr?: string             // 错误处理方式
  readonly defaultErrMsg?: string         // 默认错误信息
  readonly spErrList?: Array<string>      // 特殊错误码列表
  readonly encrypt?: boolean              // 是否加密
  readonly previewData?: any              // 编辑环境模拟数据
  readonly noLoading?: boolean            // 是否显示loading
  readonly showRtnMsg?: boolean           // 是否显示后端返回的错误信息
}
```

#### 核心方法
```typescript
export class requestService {
  // 处理请求的核心方法
  protected handleRequest(options: IRequestOptions) {
    return this.promiseWapper(request.post(options), handleErr, previewData)
  }

  // Promise包装器，统一返回格式 [data, error]
  protected promiseWapper(promise, handleErr, previewData): Promise<[any, any]> {
    return new Promise(resolve => {
      promise
        .then(res => {
          const { data, success } = res
          if (!data || !success) {
            resolve([undefined, data])  // 失败时返回错误
            return
          }
          resolve([data, undefined])    // 成功时返回数据
        })
        .catch(err => {
          resolve([undefined, err])     // 异常时返回错误
        })
    })
  }
}
```

### 2. 继承使用示例

```typescript
class MyService extends requestService {
  async myApi(options: any) {
    const params = {
      data: {
        method: 'my.api.method',
        ...options
      },
      handleErr: 'all'
    }
    return this.handleRequest(params)
  }
}
```

## 业务API层

### 1. activityService 类 (src/utils/service/apis.ts)

#### 主要接口列表

##### 活动相关接口
```typescript
// 活动首页查询
async homePage(option): Promise<any>
// 方法名: bangdao.gfActivity.home.page

// 活动时间校验
async activityTime(option): Promise<any>
// 方法名: bangdao.gfActivity.verify.activity.time

// 奖品查询
async getRecord(option): Promise<any>
// 方法名: bangdao.gfActivity.prize.query
```

##### 用户相关接口
```typescript
// 发送验证码
async sendCode(option): Promise<any>
// 方法名: bangdao.gfActivity.ds.sms.send

// 用户登录
async login(option): Promise<any>
// 方法名: bangdao.gfActivity.common.login

// 定位接口
async location(option): Promise<any>
// 方法名: bangdao.gfActivity.lbs.judge
```

##### 业务功能接口
```typescript
// 提交答题
async addAnswer(option): Promise<any>
// 方法名: bangdao.gfActivity.qa.answer

// 提交任务
async addSubmit(option): Promise<any>
// 方法名: bangdao.gfActivity.submit

// 获取题目
async getQuestion(option): Promise<any>
// 方法名: bangdao.gfActivity.qa.extract
```

##### 微信相关接口
```typescript
// 微信授权
async getWX(url): Promise<any>
// 方法名: bangdao.lifecycle.wxjsapi.signature
// 特殊配置: 使用不同的网关地址
```

### 2. 使用示例

#### 基本使用
```typescript
import { apis } from '@/utils/service/apis'

// 活动首页查询
const [homeData, homeError] = await apis.homePage({
  activity_id: 'ACTIVITY_ID',
  app_id: 'APP_ID'
})

if (homeError) {
  console.error('获取首页数据失败:', homeError)
  return
}

console.log('首页数据:', homeData)
```

#### 登录接口使用
```typescript
const [loginResult, loginError] = await apis.login({
  app_id: 'APP_ID',
  pubms_code: 'PUBMS_CODE',
  source: 'BANGDAO',
  relate_id: 'ACTIVITY_ID',
  act_id: 'ACTIVITY_ID',
  item_type: 'AWARD',
  busi_type: 'AWARD',
  is_rsa: 'T',
  sms_code: '123456',
  mobile: encryptedMobile  // 需要RSA加密
})

if (loginError) {
  // 处理登录失败
  const { rtn_flag, rtn_msg } = loginError
  console.error('登录失败:', rtn_msg)
  return
}

// 登录成功，保存token
localStorage.setItem(storageName, loginResult.user_token)
```

#### 微信授权使用
```typescript
const [wxResult, wxError] = await apis.getWX({
  url: window.location.href
})

if (wxError) {
  console.error('微信授权失败:', wxError)
  return
}

// 使用微信授权结果配置JS-SDK
const { timestamp, nonce_str, signature } = wxResult.rtn_data
wx.config({
  appId: 'YOUR_APP_ID',
  timestamp,
  nonceStr: nonce_str,
  signature,
  jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData']
})
```

## 加密解密机制

### 1. 加密配置

#### 请求头配置
```typescript
// 加密请求头
export const encryptHeader = {
  bdgatewayencryptiontype: 'bd66'  // base64加密
}

// 解密响应头
export const decryptHeader = {
  bdgatewayresponseneedencryptiontype: 'bd66'  // base64解密
}
```

#### 加密流程
1. **请求参数加密**: 使用Base64编码对请求参数进行加密
2. **响应数据解密**: 使用Base64解码对响应数据进行解密
3. **自动处理**: 根据环境和配置自动启用/禁用加密

### 2. 加密函数

#### 请求参数加密
```typescript
export const encyptData = params => {
  const dataParams = JSON.stringify(params)
  return {
    bdgatewayafterencryption: _encode_(dataParams)
  }
}
```

#### 响应数据解密
```typescript
export const decyptData = (encodeStr, handleErr = false) => {
  try {
    if (typeof encodeStr === 'string') {
      return JSON.parse(_decode_(encodeStr))
    } else {
      return encodeStr
    }
  } catch (error) {
    console.error('JSON parse Error in decrypt response', error)
    return handleErr ? '' : encodeStr
  }
}
```

## 错误处理机制

### 1. 错误类型

#### 网关错误 (ret_code)
- **200**: 请求成功
- **其他**: 网关级别错误

#### 业务错误 (rtn_flag)
- **9999**: 业务成功
- **51110042**: Token过期，自动跳转登录页
- **51000010**: 特殊业务错误
- **04011002**: 特殊业务错误
- **其他**: 一般业务错误

### 2. 错误处理流程

```typescript
// 1. 网关错误处理
if (ret_code !== 200) {
  // 查找错误配置
  const errorMap = window.BD_errorMap || {}
  const errorItem = errorMap[ret_code + '']
  
  if (errorItem) {
    // 显示配置的错误信息
    const errorCfg = getErrorTipConfig(errorItem)
    showErrorMsg(errorCfg)
  }
}

// 2. 业务错误处理
if (rtn_flag !== '9999') {
  if (rtn_flag === '51110042') {
    // Token过期，清除本地存储并跳转登录
    localStorage.setItem(configData.storageName, "")
    router.replace('/login')
  }
}
```

### 3. 错误提示配置

#### 全局错误配置
```typescript
window.BD_errorMap = {
  '51110042': {
    showType: 'alert',
    title: '提示',
    message: 'Token已过期，请重新登录',
    okBtnText: '确定'
  },
  'default': {
    showType: 'toast',
    message: '系统繁忙，请稍后重试'
  }
}
```

#### 错误提示类型
```typescript
export type ErrorTipParams = {
  showType?: 'toast' | 'alert' | 'confirm'  // 提示类型
  title?: string                            // 标题
  message: string                           // 消息内容
  okBtnText?: string                        // 确定按钮文本
  oKBtnHandle?: () => void                  // 确定按钮回调
  cancelBtnText?: string                    // 取消按钮文本
  cancelBtnHandle?: () => void              // 取消按钮回调
  messageAlign?: 'left' | 'right' | 'center' // 消息对齐方式
}
```

## SDK API封装

### 1. 投放相关API (src/apis/putting.ts)

```typescript
import { getPuttingList } from '@/apis/putting'

// 获取投放列表
const [puttingList, error] = await getPuttingList({
  standId: 'STAND_ID',
  cityCode: 'CITY_CODE'  // 可选
})
```

### 2. 任务相关API (src/apis/task.ts)

```typescript
import { getTaskList, completeTask } from '@/apis/task'

// 获取任务列表
const [taskList, error] = await getTaskList({
  standId: 'STAND_ID'
})

// 完成任务
const [result, error] = await completeTask({
  taskId: 'TASK_ID'
})
```

### 3. 活动相关API (src/apis/activity.ts)

```typescript
import { luckDraw, queryAwardCount } from '@/apis/activity'

// 抽奖
const [prizeResult, error] = await luckDraw({
  activityId: 'ACTIVITY_ID'
})

// 查询抽奖次数
const [countResult, error] = await queryAwardCount({
  activityId: 'ACTIVITY_ID'
})
```

## 最佳实践

### 1. 统一使用apis实例
```typescript
// ✅ 推荐：使用封装好的apis实例
import { apis } from '@/utils/service/apis'
const [data, error] = await apis.homePage(params)

// ❌ 不推荐：直接使用request
import request from '@/utils/service/request'
const response = await request.post(params)
```

### 2. 错误处理
```typescript
// ✅ 推荐：使用解构赋值处理返回结果
const [data, error] = await apis.login(params)
if (error) {
  // 处理错误
  return
}
// 使用数据
console.log(data)

// ❌ 不推荐：不处理错误情况
const [data] = await apis.login(params)
console.log(data) // 可能为undefined
```

### 3. 加密策略
```typescript
// 生产环境自动开启加密
const params = {
  data: { method: 'api.method', ...data },
  encrypt: !isTestEnv,  // 测试环境不加密，生产环境加密
  handleErr: 'all'
}
```

### 4. Loading状态管理
```typescript
// 需要自定义loading的场景
const params = {
  data: { method: 'api.method', ...data },
  noLoading: true,  // 不显示默认loading
  handleErr: 'all'
}

// 自定义loading
this.$toast.loading('自定义加载中...')
const [data, error] = await apis.myApi(params)
this.$toast.clear()
```

## 常见问题

### 1. 网络超时
**问题**: 请求超时或网络错误  
**解决**: 检查VPN连接和网关地址配置

### 2. 加密失败
**问题**: 加密解密过程中出现错误  
**解决**: 确认加密密钥配置正确，检查数据格式

### 3. Token过期
**问题**: 接口返回Token过期错误  
**解决**: 系统会自动清除本地Token并跳转到登录页

### 4. 错误提示不显示
**问题**: 接口错误时没有提示信息  
**解决**: 检查`window.BD_errorMap`错误码配置是否完整

### 5. 编辑环境调试
**问题**: 编辑环境下需要模拟接口数据  
**解决**: 使用`previewData`参数提供模拟数据

```typescript
const params = {
  data: { method: 'api.method', ...data },
  previewData: {  // 编辑环境返回的模拟数据
    ret_code: 200,
    content: { rtn_flag: '9999', rtn_data: {} }
  }
}
```
