module.exports = {
  name: 'beijing-low-carbon-energy-wechat-act',
  version: '0.0.17',
  chineseName: '',
  description: 'A Act Template project',
  snapshot: '',
  helpDoc: '',
  bizProject: '',
  trackConfig: {},
  // defaultGlobalShareInfo: false,
  // defaultPageShareInfo: false,
  externalResourcesList: [
    'https://adserving-oss.bangdao-tech.com/assets/se/editor-base.min.css',
    'https://adserving-oss.bangdao-tech.com/npm/vant@2.12/lib/index.css',
    'https://adserving-oss.bangdao-tech.com/npm/vant@2.12/lib/vant.min.js'
  ],
  cliConifg: {
    generateUmd: {
      // 是否需要对EditorMixin进行处理
      hasEditorMixin: false
    },
    // 打包上传环境设置
    build: [{
      // 赋能平台
      env: 'funeng',
      sdkPath: './sdk-funeng/index.js',
      config: {
        // 小磨盘项目id
        bizProject: '',
      }
    }, {
      env: 'default',
      sdkPath: './sdk-default/index.js',
      config: {
        // 小磨盘项目id
        // bizProject: '',
      }
    }],
  }
}
