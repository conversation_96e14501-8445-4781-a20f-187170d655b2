{"_meta": {"version": "1.0.0", "description": "接口模拟场景配置文件", "lastUpdated": "2024-06-15", "author": "Mock System"}, "scenarios": {"default": {"name": "默认场景", "description": "标准的成功响应场景，适用于正常开发", "delay": 500, "enabled": true, "apis": ["bangdao.gfActivity.home.page", "bangdao.gfActivity.verify.activity.time", "bangdao.gfActivity.common.login", "bangdao.gfActivity.ds.sms.send"]}, "development": {"name": "开发场景", "description": "快速响应，适用于开发阶段", "delay": 200, "enabled": true, "mockAll": true, "apis": []}, "testing": {"name": "测试场景", "description": "包含各种边界情况，适用于功能测试", "delay": 800, "enabled": true, "apis": ["bangdao.gfActivity.qa.extract", "bangdao.gfActivity.qa.answer", "bangdao.gfActivity.submit"]}, "demo": {"name": "演示场景", "description": "完美的演示数据，适用于产品演示", "delay": 1000, "enabled": true, "mockAll": true, "logRequests": false, "apis": []}, "error": {"name": "错误场景", "description": "模拟各种错误情况，适用于错误处理测试", "delay": 600, "enabled": true, "apis": ["bangdao.gfActivity.common.login", "bangdao.gfActivity.ds.sms.send", "bangdao.gfActivity.qa.answer"]}}, "data": {"success": {"bangdao.gfActivity.home.page": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "成功", "rtn_data": {"activityInfo": {"activityId": "SUCCESS_ACTIVITY_001", "activityName": "北京电力低碳用能活动", "activityDesc": "参与答题，赢取精美奖品", "startTime": "2024-01-01 00:00:00", "endTime": "2024-12-31 23:59:59", "status": "RUNNING"}, "userInfo": {"userId": "SUCCESS_USER_001", "mobile": "138****8888", "nickname": "测试用户", "isLogin": true}, "taskList": [{"taskId": "TASK_001", "taskName": "每日签到", "taskDesc": "每日签到获得闯关机会", "taskType": "1", "completionStatus": 2, "rewardCount": 1}, {"taskId": "TASK_002", "taskName": "观看视频", "taskDesc": "观看节电宣传视频", "taskType": "4", "completionStatus": 1, "rewardCount": 2}, {"taskId": "TASK_003", "taskName": "分享活动", "taskDesc": "分享活动给好友", "taskType": "3", "completionStatus": 0, "rewardCount": 1}], "answerTimes": 3, "currentStep": 2, "isComplete": false}}}, "bangdao.gfActivity.common.login": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "登录成功", "rtn_data": {"user_token": "SUCCESS_TOKEN_123456789", "userId": "SUCCESS_USER_001", "mobile": "138****8888", "nickname": "测试用户", "loginTime": "2024-06-15 12:00:00"}}}, "bangdao.gfActivity.qa.answer": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "答题成功", "rtn_data": {"correctCount": 2, "totalCount": 2, "score": 100, "isUp": true, "isComplete": false, "qaNum": 2, "grantResults": [{"sub_code": "AWARD_SUCCESS", "draw_prize_infos": [{"prizeId": "PRIZE_001", "prizeName": "精美礼品", "prizeDesc": "恭喜获得精美礼品一份", "prizeType": "PHYSICAL", "imgUrl": "https://example.com/prize.jpg"}]}]}}}}, "error": {"bangdao.gfActivity.common.login": {"ret_code": 200, "content": {"rtn_flag": "51000010", "rtn_msg": "手机号格式错误", "rtn_data": null}}, "bangdao.gfActivity.ds.sms.send": {"ret_code": 200, "content": {"rtn_flag": "50001", "rtn_msg": "验证码发送失败，请稍后重试", "rtn_data": null}}, "bangdao.gfActivity.qa.answer": {"ret_code": 200, "content": {"rtn_flag": "51110042", "rtn_msg": "Token已过期，请重新登录", "rtn_data": null}}}, "demo": {"bangdao.gfActivity.home.page": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "成功", "rtn_data": {"activityInfo": {"activityId": "DEMO_ACTIVITY_001", "activityName": "北京电力低碳用能活动", "activityDesc": "参与答题，赢取精美奖品", "status": "RUNNING"}, "userInfo": {"userId": "DEMO_USER_001", "mobile": "138****8888", "nickname": "演示用户", "isLogin": true}, "taskList": [{"taskId": "TASK_001", "taskName": "每日签到", "taskDesc": "每日签到获得闯关机会", "taskType": "1", "completionStatus": 2, "rewardCount": 1}, {"taskId": "TASK_002", "taskName": "观看视频", "taskDesc": "观看节电宣传视频", "taskType": "4", "completionStatus": 2, "rewardCount": 2}, {"taskId": "TASK_003", "taskName": "分享活动", "taskDesc": "分享活动给好友", "taskType": "3", "completionStatus": 2, "rewardCount": 1}], "answerTimes": 10, "currentStep": 4, "isComplete": true}}}, "bangdao.gfActivity.qa.answer": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "答题成功", "rtn_data": {"correctCount": 2, "totalCount": 2, "score": 100, "isUp": true, "isComplete": true, "qaNum": 5, "grantResults": [{"sub_code": "AWARD_SUCCESS", "draw_prize_infos": [{"prizeId": "DEMO_PRIZE_001", "prizeName": "演示奖品", "prizeDesc": "恭喜获得演示奖品一份", "prizeType": "PHYSICAL", "imgUrl": "https://example.com/demo-prize.jpg"}]}]}}}}, "noprize": {"bangdao.gfActivity.qa.answer": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "答题成功", "rtn_data": {"correctCount": 2, "totalCount": 2, "score": 100, "isUp": false, "isComplete": false, "qaNum": 2, "grantResults": []}}}}}, "templates": {"success_response": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "成功", "rtn_data": {}}}, "error_response": {"ret_code": 200, "content": {"rtn_flag": "50001", "rtn_msg": "系统错误", "rtn_data": null}}, "token_expired": {"ret_code": 200, "content": {"rtn_flag": "51110042", "rtn_msg": "Token已过期，请重新登录", "rtn_data": null}}}}