/**
 * Mock Service Worker
 * 接口模拟服务工作线程
 * 
 * 功能：
 * 1. 拦截网络请求
 * 2. 根据配置返回模拟数据
 * 3. 支持动态开启/关闭
 * 4. 支持延迟响应模拟
 */

const CACHE_NAME = 'mock-api-cache-v1'
const CONFIG_KEY = 'mock-config'
const MOCK_DATA_KEY = 'mock-data'

// 默认配置
const DEFAULT_CONFIG = {
  enabled: false,
  delay: 500,
  logRequests: true,
  mockAll: false,
  enabledApis: []
}

// 默认模拟数据
const DEFAULT_MOCK_DATA = {}

let mockConfig = DEFAULT_CONFIG
let mockData = DEFAULT_MOCK_DATA

// 监听安装事件
self.addEventListener('install', (event) => {
  console.log('[Mock SW] Installing...')
  event.waitUntil(
    caches.open(CACHE_NAME).then(() => {
      console.log('[Mock SW] Cache opened')
      // 强制激活新的 Service Worker
      return self.skipWaiting()
    })
  )
})

// 监听激活事件
self.addEventListener('activate', (event) => {
  console.log('[Mock SW] Activating...')
  event.waitUntil(
    Promise.all([
      // 清理旧缓存
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              return caches.delete(cacheName)
            }
          })
        )
      }),
      // 立即控制所有客户端
      self.clients.claim(),
      // 初始化配置
      initializeConfig()
    ])
  )
})

// 初始化配置
async function initializeConfig() {
  try {
    // 从 IndexedDB 或 localStorage 读取配置
    const savedConfig = await getStoredConfig()
    const savedMockData = await getStoredMockData()
    
    mockConfig = { ...DEFAULT_CONFIG, ...savedConfig }
    mockData = { ...DEFAULT_MOCK_DATA, ...savedMockData }
    
    console.log('[Mock SW] Configuration loaded:', mockConfig)
  } catch (error) {
    console.error('[Mock SW] Failed to load configuration:', error)
  }
}

// 获取存储的配置
async function getStoredConfig() {
  try {
    // 尝试从客户端获取配置
    const clients = await self.clients.matchAll()
    if (clients.length > 0) {
      return new Promise((resolve) => {
        const messageChannel = new MessageChannel()
        messageChannel.port1.onmessage = (event) => {
          resolve(event.data.config || {})
        }
        clients[0].postMessage({
          type: 'GET_CONFIG'
        }, [messageChannel.port2])
      })
    }
    return {}
  } catch (error) {
    console.error('[Mock SW] Error getting stored config:', error)
    return {}
  }
}

// 获取存储的模拟数据
async function getStoredMockData() {
  try {
    const clients = await self.clients.matchAll()
    if (clients.length > 0) {
      return new Promise((resolve) => {
        const messageChannel = new MessageChannel()
        messageChannel.port1.onmessage = (event) => {
          resolve(event.data.mockData || {})
        }
        clients[0].postMessage({
          type: 'GET_MOCK_DATA'
        }, [messageChannel.port2])
      })
    }
    return {}
  } catch (error) {
    console.error('[Mock SW] Error getting stored mock data:', error)
    return {}
  }
}

// 监听消息事件
self.addEventListener('message', (event) => {
  const { type, data } = event.data
  
  switch (type) {
    case 'UPDATE_CONFIG':
      mockConfig = { ...mockConfig, ...data }
      console.log('[Mock SW] Config updated:', mockConfig)
      break
      
    case 'UPDATE_MOCK_DATA':
      mockData = { ...mockData, ...data }
      console.log('[Mock SW] Mock data updated')
      break
      
    case 'GET_STATUS':
      event.ports[0].postMessage({
        enabled: mockConfig.enabled,
        config: mockConfig,
        mockDataKeys: Object.keys(mockData)
      })
      break
      
    case 'CLEAR_CACHE':
      caches.delete(CACHE_NAME)
      console.log('[Mock SW] Cache cleared')
      break
  }
})

// 监听网络请求
self.addEventListener('fetch', (event) => {
  // 只处理 API 请求
  if (!isApiRequest(event.request)) {
    return
  }
  
  // 如果模拟功能未启用，直接放行
  if (!mockConfig.enabled) {
    return
  }
  
  event.respondWith(handleApiRequest(event.request))
})

// 判断是否为 API 请求
function isApiRequest(request) {
  const url = new URL(request.url)
  
  // 检查是否为网关请求
  const isGatewayRequest = url.pathname.includes('gateway.do') || 
                          url.hostname.includes('bangdao-tech.com')
  
  // 检查是否为 POST 请求
  const isPostRequest = request.method === 'POST'
  
  return isGatewayRequest && isPostRequest
}

// 处理 API 请求
async function handleApiRequest(request) {
  try {
    // 解析请求数据
    const requestData = await parseRequestData(request)
    const apiMethod = requestData.method
    
    if (mockConfig.logRequests) {
      console.log('[Mock SW] Intercepted API request:', apiMethod, requestData)
    }
    
    // 检查是否需要模拟此接口
    if (!shouldMockApi(apiMethod)) {
      // 不模拟，发送真实请求
      return fetch(request)
    }
    
    // 获取模拟数据
    const mockResponse = getMockResponse(apiMethod, requestData)
    
    if (!mockResponse) {
      console.warn('[Mock SW] No mock data found for:', apiMethod)
      return fetch(request)
    }
    
    // 模拟延迟
    if (mockConfig.delay > 0) {
      await new Promise(resolve => setTimeout(resolve, mockConfig.delay))
    }
    
    // 返回模拟响应
    return new Response(JSON.stringify(mockResponse), {
      status: 200,
      statusText: 'OK',
      headers: {
        'Content-Type': 'application/json',
        'X-Mock-Response': 'true'
      }
    })
    
  } catch (error) {
    console.error('[Mock SW] Error handling request:', error)
    // 出错时发送真实请求
    return fetch(request)
  }
}

// 解析请求数据
async function parseRequestData(request) {
  try {
    const clonedRequest = request.clone()
    const body = await clonedRequest.text()
    
    // 尝试解析 JSON
    try {
      return JSON.parse(body)
    } catch {
      // 如果不是 JSON，尝试解析表单数据
      const formData = new URLSearchParams(body)
      const result = {}
      for (const [key, value] of formData) {
        result[key] = value
      }
      return result
    }
  } catch (error) {
    console.error('[Mock SW] Error parsing request data:', error)
    return {}
  }
}

// 判断是否应该模拟此接口
function shouldMockApi(apiMethod) {
  if (mockConfig.mockAll) {
    return true
  }
  
  return mockConfig.enabledApis.includes(apiMethod)
}

// 获取模拟响应数据
function getMockResponse(apiMethod, requestData) {
  const mockResponseData = mockData[apiMethod]
  
  if (!mockResponseData) {
    return null
  }
  
  // 如果是函数，执行函数获取动态数据
  if (typeof mockResponseData === 'function') {
    return mockResponseData(requestData)
  }
  
  // 如果是对象，直接返回
  return mockResponseData
}

console.log('[Mock SW] Service Worker loaded')
