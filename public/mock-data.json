{"bangdao.gfActivity.home.page": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "成功", "rtn_data": {"activityInfo": {"activityId": "MOCK_ACTIVITY_001", "activityName": "北京电力低碳用能活动", "activityDesc": "参与答题，赢取精美奖品", "startTime": "2024-01-01 00:00:00", "endTime": "2024-12-31 23:59:59", "status": "RUNNING"}, "userInfo": {"userId": "MOCK_USER_001", "mobile": "138****8888", "nickname": "测试用户", "isLogin": true}, "taskList": [{"taskId": "TASK_001", "taskName": "每日签到", "taskDesc": "每日签到获得闯关机会", "taskType": "1", "completionStatus": 1, "rewardCount": 1}, {"taskId": "TASK_002", "taskName": "观看视频", "taskDesc": "观看节电宣传视频", "taskType": "4", "completionStatus": 0, "rewardCount": 2}, {"taskId": "TASK_003", "taskName": "分享活动", "taskDesc": "分享活动给好友", "taskType": "3", "completionStatus": 2, "rewardCount": 1}], "answerTimes": 3, "currentStep": 2, "isComplete": false}}}, "bangdao.gfActivity.verify.activity.time": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "成功", "rtn_data": {"status": "RUNNING", "startTime": "2024-01-01 00:00:00", "endTime": "2024-12-31 23:59:59", "currentTime": "2024-06-15 12:00:00"}}}, "bangdao.gfActivity.ds.sms.send": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "验证码发送成功", "rtn_data": {"mobile": "138****8888", "sendTime": "2024-06-15 12:00:00"}}}, "bangdao.gfActivity.common.login": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "登录成功", "rtn_data": {"user_token": "MOCK_TOKEN_123456789", "userId": "MOCK_USER_001", "mobile": "138****8888", "nickname": "测试用户", "loginTime": "2024-06-15 12:00:00"}}}, "bangdao.gfActivity.qa.extract": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "成功", "rtn_data": {"questions": [{"questionId": "Q001", "questionText": "以下哪种行为最能体现低碳生活理念？", "questionType": "single", "options": [{"optionId": "A", "optionText": "随手关灯"}, {"optionId": "B", "optionText": "长时间开空调"}, {"optionId": "C", "optionText": "频繁使用电梯"}, {"optionId": "D", "optionText": "24小时开电视"}], "correctAnswer": "A"}, {"questionId": "Q002", "questionText": "家庭节电的最佳做法是？", "questionType": "single", "options": [{"optionId": "A", "optionText": "使用节能电器"}, {"optionId": "B", "optionText": "增加用电设备"}, {"optionId": "C", "optionText": "忽略电器待机功耗"}, {"optionId": "D", "optionText": "不关注电费账单"}], "correctAnswer": "A"}], "totalQuestions": 2, "timeLimit": 300}}}, "bangdao.gfActivity.qa.answer": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "答题成功", "rtn_data": {"correctCount": 2, "totalCount": 2, "score": 100, "isUp": true, "isComplete": false, "qaNum": 2, "grantResults": [{"sub_code": "AWARD_SUCCESS", "draw_prize_infos": [{"prizeId": "PRIZE_001", "prizeName": "精美礼品", "prizeDesc": "恭喜获得精美礼品一份", "prizeType": "PHYSICAL", "imgUrl": "https://example.com/prize.jpg"}]}]}}}, "bangdao.gfActivity.submit": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "任务完成", "rtn_data": {"taskId": "TASK_002", "isComplete": false, "rewardCount": 1, "totalRewardCount": 3}}}, "bangdao.gfActivity.prize.query": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "成功", "rtn_data": {"prizeList": [{"prizeId": "PRIZE_001", "prizeName": "精美礼品", "prizeDesc": "恭喜获得精美礼品一份", "prizeType": "PHYSICAL", "imgUrl": "https://example.com/prize.jpg", "status": "RECEIVED", "receiveTime": "2024-06-15 12:00:00"}], "totalCount": 1}}}, "bangdao.gfActivity.lbs.judge": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "定位成功", "rtn_data": {"latitude": 39.9042, "longitude": 116.4074, "address": "北京市东城区", "cityCode": "110100", "cityName": "北京市"}}}, "bangdao.lifecycle.wxjsapi.signature": {"ret_code": 200, "content": {"rtn_flag": "9999", "rtn_msg": "成功", "rtn_data": {"appId": "wxd26f4d35936b226d", "timestamp": "1718438400", "nonce_str": "mock_nonce_str_123456", "signature": "mock_signature_abcdef123456", "jsapi_ticket": "mock_ticket_789012"}}}}